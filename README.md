# 周同学活动介绍网页版

基于 Vue 3 和腾讯云开发的周同学厦门地铁主题活动宣传网页，集成小红书搜索MCP工具。

[![Powered by CloudBase](https://7463-tcb-advanced-a656fc-1257967285.tcb.qcloud.la/mcp/powered-by-cloudbase-badge.svg)](https://github.com/TencentCloudBase/CloudBase-AI-ToolKit)

> 本项目基于 [**CloudBase AI ToolKit**](https://github.com/TencentCloudBase/CloudBase-AI-ToolKit) 开发，通过AI提示词和 MCP 协议+云开发，让开发更智能、更高效。

## 项目简介

这是一个为周同学（周杰伦）厦门地铁主题活动打造的互动宣传网页，提供活动信息展示、打卡点导览、积分攻略、社交媒体内容搜索等功能。

**🎵 访问地址**: https://huiyi-7gudxwsnf4ca4b67-1256836374.tcloudbaseapp.com/zhou-activities

## 核心功能

### 🎵 活动展示
- **英雄区域**: 动态音符动画，活动倒计时
- **核心信息**: 活动时间、地点、参与方式
- **实时数据**: 打卡次数、库存状态、中奖名单

### 🎯 打卡导览
- **六大打卡点**: 十里长堤、海上世界、SM城市广场、中山路、鼓浪屿、荻花洲
- **详细攻略**: 最佳拍摄时间、推荐装备、实用技巧
- **互动地图**: 点击卡片查看详情，提供导航功能

### 🎁 积分体系
- **获取方式**: 签到、乘车、邀请好友、打卡、NPC互动
- **兑换奖励**: 星际光轮篮球、唱游卡、满减券、停车券
- **实时库存**: 动态更新商品库存状态

### 📱 社交搜索
- **小红书搜索**: 集成MCP工具，搜索相关笔记和用户
- **热门标签**: 快速搜索常用关键词
- **内容展示**: 点赞、评论、分享数据可视化

### 🗺️ 游览路线
- **经典一日游**: 完整的六点游览路线
- **快闪购物游**: 专注限量商品抢购
- **积分刷豆游**: 最大化积分获取策略

## 技术架构

### 前端技术栈
- **Vue 3**: 组合式API，响应式设计
- **Vite**: 快速构建工具，热重载
- **Tailwind CSS + DaisyUI**: 现代化UI框架
- **JavaScript ES6+**: 现代JavaScript特性

### 云开发集成
- **环境ID**: `huiyi-7gudxwsnf4ca4b67`
- **静态托管**: 部署路径 `/zhou-activities`
- **访问地址**: https://huiyi-7gudxwsnf4ca4b67-1256836374.tcloudbaseapp.com/zhou-activities

### MCP工具集成 (v3.0)
- **增强版MCP服务器**: 支持真实数据获取模式
- **6大核心工具**: 搜索、统计、内容获取、网络搜索等
- **实时数据支持**: 活动统计、热门话题、用户动态
- **智能数据生成**: 基于时间和关键词的真实感数据
- **完善错误处理**: 缓存机制、降级策略、异常处理

### 新增功能特性
- **实时数据监控**: 动态统计面板，每5秒更新
- **内容流展示**: 无限滚动的小红书内容流
- **图片查看器**: 全屏图片浏览功能
- **数据切换**: 一键切换真实/模拟数据模式
- **响应式优化**: 完美适配移动端和桌面端

## 项目结构

```
周同学活动介绍网页版/
├── src/
│   ├── components/
│   │   ├── XiaoHongShuSearch.vue    # 小红书搜索组件
│   │   ├── RealtimeStats.vue        # 实时数据监控组件 🆕
│   │   ├── ContentFeed.vue          # 内容流展示组件 🆕
│   │   └── Footer.vue               # 页脚组件
│   ├── pages/
│   │   └── HomePage.vue             # 主页组件（已大幅优化）
│   ├── services/
│   │   ├── mcpSearchService.js      # MCP搜索服务
│   │   └── realDataService.js       # 真实数据服务 🆕
│   ├── utils/
│   │   └── cloudbase.js             # 云开发配置
│   ├── App.vue                      # 应用入口
│   ├── main.js                      # 主入口文件
│   └── style.css                    # 全局样式
├── cloudfunctions/
│   └── hello/                       # 示例云函数
├── MCP工具/
│   ├── xiaohongshu_mcp_simple.py    # 小红书搜索MCP服务器（简单版）
│   ├── xiaohongshu_mcp_enhanced.py  # 小红书搜索MCP服务器（v3.0增强版）🆕
│   ├── .mcp.json                    # MCP配置文件
│   ├── 小红书搜索MCP使用说明.md       # 简单版使用文档
│   └── 小红书MCP增强版使用指南.md     # 增强版使用指南
├── 文档/
│   ├── 优化升级说明.md               # 详细升级说明 🆕
│   ├── cloudbaserc.json             # 云开发部署配置
│   ├── vite.config.js               # Vite配置
│   └── tailwind.config.js           # Tailwind配置
└── README.md                        # 项目说明（已更新）
```

## 快速开始

### 前提条件
- Node.js (版本 16 或更高)
- 腾讯云开发账号
- Python 3.8+ (用于MCP工具)

### 1. 克隆项目

```bash
git clone <项目地址>
cd 周同学活动介绍网页版
```

### 2. 安装依赖

```bash
npm install
```

### 3. 配置云开发环境

编辑 `src/utils/cloudbase.js`，设置环境ID：

```javascript
const ENV_ID = 'huiyi-7gudxwsnf4ca4b67'
```

### 4. 启动开发服务器

```bash
npm run dev
```

### 5. 构建生产版本

```bash
npm run build
```

### 6. 部署到云开发

```bash
tcb framework deploy
```

## 活动详情

### 📅 活动时间
即日起至 **2025年7月20日**

### 📍 活动地点
- **主题专列**: 厦门地铁1号线
- **打卡点**: 6大地标景点
- **快闪店**: 3大商圈

### 🎯 六大打卡点
1. **十里长堤5.5米大娃** - 草坪音乐会 (地铁1号线)
2. **海上世界音乐台阶** - AR互动体验 (地铁2号线)
3. **SM城市广场7米雕塑** - 购物便利 (地铁1号线)
4. **中山路骑楼街区** - 闽南文化 (地铁1号线)
5. **鼓浪屿IP元素** - 钢琴之岛 (轮渡+步行)
6. **荻花洲艺术装置** - 现代艺术 (地铁3号线)

### 🎁 积分获取方式
- **每日签到**: 50豆
- **扫码乘车**: 100豆
- **邀请好友**: 100豆/人 (上限500豆/日)
- **打卡扫码**: 200豆/次 (每日2次)
- **NPC互动**: 50-100豆 (7月11-13日)

### 🏆 热门奖励
- **星际光轮篮球**: 2000豆 (限量每日5个)
- **9.9元唱游卡**: 8次乘车+餐饮券
- **快闪店满减券**: 300豆 (满99减20)
- **城市限定冰箱贴**: 小红书集赞118获得

## 开发指南

### 添加新功能

1. **创建组件**
```bash
# 在 src/components/ 创建新组件
touch src/components/NewComponent.vue
```

2. **集成到主页**
```vue
<!-- 在 src/pages/HomePage.vue 中导入 -->
<script setup>
import NewComponent from '../components/NewComponent.vue'
</script>
```

### 修改搜索功能

1. **编辑搜索服务**
```javascript
// src/services/mcpSearchService.js
export const searchCustomContent = async (keyword) => {
  // 添加新的搜索逻辑
}
```

2. **更新搜索组件**
```vue
<!-- src/components/XiaoHongShuSearch.vue -->
<script setup>
import { searchCustomContent } from '../services/mcpSearchService.js'
</script>
```

### 更新活动内容

1. **修改活动数据**
```javascript
// 在 src/pages/HomePage.vue 中更新
const checkinPoints = ref([
  // 更新打卡点信息
])

const pointTasks = ref([
  // 更新积分任务
])
```

2. **调整时间和奖励**
```javascript
// 更新活动结束时间
function calculateRemainingDays() {
  const endDate = new Date('2025-07-20')
  // ...
}
```

## MCP工具使用

### 小红书搜索MCP

我们集成了自定义的小红书搜索MCP工具，支持：

1. **笔记搜索**
```python
# 搜索相关笔记
python3 xiaohongshu_mcp_simple.py
```

2. **用户搜索**
```python
# 搜索相关用户
python3 xiaohongshu_mcp_simple.py
```

3. **前端集成**
```javascript
// 在Vue组件中使用
import { searchXiaoHongShuNotes } from '../services/mcpSearchService.js'

const results = await searchXiaoHongShuNotes('周同学厦门地铁')
```

### 配置MCP工具

编辑 `.mcp.json` 配置文件：

```json
{
  "mcpServers": {
    "xiaohongshu-search": {
      "command": "python3",
      "args": ["xiaohongshu_mcp_simple.py"]
    }
  }
}
```

## 部署说明

### 当前部署状态
- **环境**: 腾讯云开发生产环境
- **状态**: ✅ 已部署运行
- **访问**: https://huiyi-7gudxwsnf4ca4b67-1256836374.tcloudbaseapp.com/zhou-activities

### 部署配置

```json
{
  "framework": {
    "name": "cloudbase-vue-template",
    "cloudPath": "/zhou-activities"
  }
}
```

### 手动部署步骤

1. **构建项目**
```bash
npm run build
```

2. **登录云开发**
```bash
tcb login
```

3. **部署到云开发**
```bash
tcb framework deploy
```

## 维护指南

### 定期更新任务

1. **活动数据更新**
   - 更新库存状态
   - 刷新中奖名单
   - 调整活动时间

2. **搜索内容更新**
   - 刷新热门话题
   - 更新搜索标签
   - 优化搜索结果

3. **系统监控**
   - 检查服务可用性
   - 监控访问量
   - 处理错误日志

### 故障排除

1. **搜索功能异常**
   - 检查MCP工具状态
   - 验证搜索服务配置
   - 确认降级策略正常

2. **部署问题**
   - 确认云开发环境配置
   - 检查域名解析
   - 验证CDN缓存

3. **样式和交互异常**
   - 检查CSS构建
   - 验证组件状态
   - 测试响应式设计

## 性能优化

### 前端优化
- 图片懒加载
- 组件按需加载
- 资源压缩和缓存

### 后端优化
- CDN加速
- 静态资源优化
- 数据库查询优化

## 安全考虑

- 用户数据保护
- API接口安全
- 跨域请求控制
- 输入验证和过滤

## 联系信息

- **项目维护**: AI开发助手
- **技术支持**: 腾讯云开发团队
- **活动咨询**: 厦门文旅官方

---

**🎵 让我们一起在音乐的海洋中遇见最美的厦门！**

## 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 相关链接

- [Vue 3 官方文档](https://v3.vuejs.org/)
- [Vite 官方文档](https://vitejs.dev/)
- [Tailwind CSS 文档](https://tailwindcss.com/)
- [腾讯云开发文档](https://cloud.tencent.com/document/product/876)
- [CloudBase AI ToolKit](https://github.com/TencentCloudBase/CloudBase-AI-ToolKit)