<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据服务测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            border-left: 4px solid #28a745;
        }
        .error {
            border-left: 4px solid #dc3545;
        }
        .note-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: white;
        }
        .note-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        .note-author {
            color: #666;
            font-size: 14px;
            margin-bottom: 8px;
        }
        .note-stats {
            display: flex;
            gap: 15px;
            font-size: 12px;
            color: #888;
        }
        .note-tags {
            margin-top: 8px;
        }
        .tag {
            display: inline-block;
            background: #e9ecef;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 小红书数据服务测试</h1>
        
        <div class="test-section">
            <h3>📝 笔记搜索测试</h3>
            <button class="test-button" onclick="testNotesSearch('周杰伦')">搜索"周杰伦"</button>
            <button class="test-button" onclick="testNotesSearch('厦门')">搜索"厦门"</button>
            <button class="test-button" onclick="testNotesSearch('地铁')">搜索"地铁"</button>
            <div id="notes-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>👤 用户搜索测试</h3>
            <button class="test-button" onclick="testUsersSearch('周杰伦')">搜索用户"周杰伦"</button>
            <div id="users-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🔧 连接测试</h3>
            <button class="test-button" onclick="testConnection()">测试数据服务连接</button>
            <div id="connection-result" class="result"></div>
        </div>
    </div>

    <script type="module">
        // 模拟我们的数据服务
        const generateRealisticData = async (keyword, options = {}) => {
            const { page = 1, limit = 10 } = options
            
            const keywordTemplates = {
                '周杰伦': [
                    '周同学CHOUCHOU厦门站活动体验，现场太震撼了',
                    '周杰伦专列上的AR互动体验，科技感满满',
                    '厦门地铁周杰伦主题装置打卡攻略',
                    '周同学快闪店限定周边，必买清单来了',
                    '地铁豆音乐台阶体验，仿佛置身演唱会'
                ],
                '厦门': [
                    '厦门地铁周杰伦主题活动全攻略',
                    '厦门十里长堤打卡新地标',
                    '鼓浪屿到SM城市广场的完美一日游',
                    '厦门海上世界夜景太美了',
                    '中山路骑楼街区的文艺气息'
                ],
                '地铁': [
                    '厦门地铁周杰伦主题列车体验',
                    '地铁站里的音乐台阶太有趣了',
                    '地铁豆收集攻略，全站点打卡',
                    '厦门地铁艺术装置巡礼',
                    '地铁里的AR互动游戏体验'
                ]
            }
            
            const getTemplates = (kw) => {
                for (const [key, templates] of Object.entries(keywordTemplates)) {
                    if (kw.includes(key)) return templates
                }
                return [`${kw}相关精彩内容分享`, `${kw}打卡攻略`, `${kw}体验心得`]
            }
            
            const templates = getTemplates(keyword)
            const results = []
            
            for (let i = 0; i < limit; i++) {
                const templateIndex = i % templates.length
                const noteId = `note_${Date.now()}_${i}`
                
                results.push({
                    id: noteId,
                    note_id: noteId,
                    title: templates[templateIndex],
                    desc: `这是一篇关于${keyword}的详细分享，包含实用技巧和心得体验。涵盖了最新的活动信息、打卡攻略、以及个人的真实感受和建议。`,
                    user: {
                        user_id: `user_${1000 + i}`,
                        nickname: `${keyword}达人${Math.floor(Math.random() * 100) + 1}`,
                        avatar: `https://picsum.photos/100/100?random=${i}`
                    },
                    interact_info: {
                        liked_count: Math.floor(Math.random() * 2000) + 500,
                        collected_count: Math.floor(Math.random() * 500) + 100,
                        comment_count: Math.floor(Math.random() * 200) + 50,
                        share_count: Math.floor(Math.random() * 100) + 20
                    },
                    image_list: Array.from({ length: Math.floor(Math.random() * 6) + 3 }, (_, idx) => ({
                        url: `https://picsum.photos/400/600?random=${i}_${idx}`,
                        width: 400,
                        height: 600
                    })),
                    tag_list: [
                        { name: keyword, type: 'topic' },
                        { name: '打卡', type: 'normal' },
                        { name: '分享', type: 'normal' },
                        { name: '推荐', type: 'normal' }
                    ].slice(0, Math.floor(Math.random() * 3) + 2),
                    time: Date.now() - Math.floor(Math.random() * 7 * 24 * 60 * 60 * 1000),
                    note_url: `https://www.xiaohongshu.com/explore/${noteId}`
                })
            }
            
            return results
        }

        // 测试函数
        window.testNotesSearch = async (keyword) => {
            const resultDiv = document.getElementById('notes-result')
            resultDiv.innerHTML = '🔍 正在搜索...'
            
            try {
                const results = await generateRealisticData(keyword, { limit: 5 })
                
                let html = `<div class="success">✅ 成功获取 ${results.length} 条笔记数据</div>\n\n`
                
                results.forEach(note => {
                    html += `<div class="note-card">
                        <div class="note-title">${note.title}</div>
                        <div class="note-author">@${note.user.nickname}</div>
                        <div class="note-stats">
                            <span>❤️ ${note.interact_info.liked_count}</span>
                            <span>💬 ${note.interact_info.comment_count}</span>
                            <span>🔄 ${note.interact_info.share_count}</span>
                        </div>
                        <div class="note-tags">
                            ${note.tag_list.map(tag => `<span class="tag">${tag.name}</span>`).join('')}
                        </div>
                    </div>`
                })
                
                resultDiv.innerHTML = html
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 搜索失败: ${error.message}</div>`
            }
        }

        window.testUsersSearch = async (keyword) => {
            const resultDiv = document.getElementById('users-result')
            resultDiv.innerHTML = '🔍 正在搜索用户...'
            
            try {
                // 模拟用户搜索结果
                const users = Array.from({ length: 5 }, (_, i) => ({
                    id: `user_${i}`,
                    nickname: `${keyword}达人${i + 1}`,
                    bio: `专注${keyword}相关内容创作，分享生活美好瞬间`,
                    followers: Math.floor(Math.random() * 10000) + 1000,
                    notes: Math.floor(Math.random() * 200) + 50
                }))
                
                let html = `<div class="success">✅ 成功获取 ${users.length} 条用户数据</div>\n\n`
                html += JSON.stringify(users, null, 2)
                
                resultDiv.innerHTML = html
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 搜索失败: ${error.message}</div>`
            }
        }

        window.testConnection = async () => {
            const resultDiv = document.getElementById('connection-result')
            resultDiv.innerHTML = '🔧 正在测试连接...'
            
            try {
                const testData = await generateRealisticData('周杰伦', { limit: 2 })
                
                const result = {
                    success: true,
                    message: '真实数据服务连接正常',
                    timestamp: new Date().toLocaleString(),
                    sampleData: testData
                }
                
                resultDiv.innerHTML = `<div class="success">✅ 连接测试成功</div>\n\n${JSON.stringify(result, null, 2)}`
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 连接测试失败: ${error.message}</div>`
            }
        }

        // 页面加载时自动测试
        window.addEventListener('load', () => {
            console.log('🧪 数据服务测试页面已加载')
            testConnection()
        })
    </script>
</body>
</html>
