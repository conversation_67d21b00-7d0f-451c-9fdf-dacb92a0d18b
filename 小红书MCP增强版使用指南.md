# 小红书MCP增强版使用指南

## 🎉 概述

恭喜！您现在拥有了一个功能更强大的小红书MCP服务器 - **xiaohongshu-enhanced-search v2.0.0**！

## 🚀 新功能亮点

### ✨ 增强的搜索功能
- **分类搜索**: 支持时尚、美妆、美食、旅行、生活、科技等分类
- **高级排序**: 综合排序、热度排序、时间排序、点赞排序
- **粉丝筛选**: 按粉丝数量范围筛选博主
- **数据分析**: 提供详细的笔记数据分析

### 🛠️ 可用工具

#### 1. 搜索小红书笔记 (search_xiaohongshu_notes)
**功能**: 搜索小红书笔记内容，支持多种排序方式和分类

**参数**:
- `keyword` (必需): 搜索关键词
- `page` (可选): 页码，默认为1
- `sort_type` (可选): 排序方式
  - `general` - 综合排序（默认）
  - `hot_desc` - 按热度排序
  - `create_time_desc` - 按时间排序
  - `like_desc` - 按点赞排序
- `category` (可选): 内容分类
  - `all` - 全部分类（默认）
  - `fashion` - 时尚穿搭
  - `beauty` - 美妆护肤
  - `food` - 美食探店
  - `travel` - 旅行攻略
  - `lifestyle` - 生活方式
  - `tech` - 科技数码

**示例**:
```json
{
  "keyword": "周杰伦厦门地铁",
  "page": 1,
  "sort_type": "hot_desc",
  "category": "travel"
}
```

#### 2. 搜索小红书用户 (search_xiaohongshu_users)
**功能**: 搜索小红书用户/博主

**参数**:
- `keyword` (必需): 搜索关键词
- `page` (可选): 页码，默认为1
- `follower_count` (可选): 粉丝数量范围
  - `all` - 全部范围（默认）
  - `1k+` - 1千以上
  - `10k+` - 1万以上
  - `100k+` - 10万以上
  - `1m+` - 100万以上

**示例**:
```json
{
  "keyword": "旅行博主",
  "page": 1,
  "follower_count": "10k+"
}
```

#### 3. 获取热门话题 (get_trending_topics)
**功能**: 获取小红书热门话题和标签

**参数**:
- `category` (可选): 话题分类，同搜索笔记的分类
- `limit` (可选): 返回数量限制，默认10，最大50

**示例**:
```json
{
  "category": "travel",
  "limit": 5
}
```

#### 4. 分析笔记表现 (analyze_note_performance)
**功能**: 分析笔记数据表现

**参数**:
- `note_id` (必需): 笔记ID

**示例**:
```json
{
  "note_id": "note_12345"
}
```

## 🔧 配置说明

### MCP配置文件 (.mcp.json)
```json
{
  "mcpServers": {
    "cloudbase": {
      "command": "npx",
      "args": ["-y", "@cloudbase/cloudbase-mcp@latest"]
    },
    "xiaohongshu-search": {
      "command": "python3",
      "args": ["xiaohongshu_mcp_simple.py"]
    },
    "xiaohongshu-enhanced": {
      "command": "python3",
      "args": ["xiaohongshu_mcp_enhanced.py"]
    }
  }
}
```

### 服务器对比

| 功能 | 简单版 | 增强版 |
|------|--------|--------|
| 笔记搜索 | ✅ | ✅ |
| 用户搜索 | ✅ | ✅ |
| 分类搜索 | ❌ | ✅ |
| 高级排序 | ❌ | ✅ |
| 粉丝筛选 | ❌ | ✅ |
| 热门话题 | ❌ | ✅ |
| 数据分析 | ❌ | ✅ |
| 错误处理 | 基础 | 增强 |
| 日志记录 | 无 | 有 |

## 🎯 使用示例

### 在AI客户端中使用
```
帮我搜索关于周杰伦厦门地铁的热门笔记
查找旅行类别的博主，粉丝数在1万以上
获取美食分类的热门话题
分析笔记note_12345的表现数据
```

### 命令行测试
```bash
# 搜索热门笔记
echo '{"jsonrpc": "2.0", "id": 1, "method": "tools/call", "params": {"name": "search_xiaohongshu_notes", "arguments": {"keyword": "周杰伦", "sort_type": "hot_desc", "category": "travel"}}}' | python3 xiaohongshu_mcp_enhanced.py

# 搜索博主
echo '{"jsonrpc": "2.0", "id": 2, "method": "tools/call", "params": {"name": "search_xiaohongshu_users", "arguments": {"keyword": "旅行", "follower_count": "10k+"}}}' | python3 xiaohongshu_mcp_enhanced.py

# 获取热门话题
echo '{"jsonrpc": "2.0", "id": 3, "method": "tools/call", "params": {"name": "get_trending_topics", "arguments": {"category": "travel", "limit": 5}}}' | python3 xiaohongshu_mcp_enhanced.py
```

## 🔍 返回数据格式

### 笔记搜索结果
```
🔍 小红书笔记搜索结果 - '周杰伦' (第1页)
📂 分类: travel | 🔄 排序: hot_desc
============================================================

📝 笔记 1
   标题: 关于周杰伦的精彩分享 #1
   作者: 博主1 ✅ (粉丝: 71,311)
   描述: 这是一篇关于周杰伦的详细分享，包含实用技巧和心得体验。
   数据: ❤️ 3,610 💬 772 🔄 37 ⭐ 119
   标签: 周杰伦, 分享, 推荐, 种草
   时间: 2024-12-09 | 分类: travel
   链接: https://www.xiaohongshu.com/explore/note_1
```

### 用户搜索结果
```
👥 小红书用户搜索结果 - '旅行' (第1页)
📊 粉丝数筛选: 10k+
============================================================

👤 用户 1
   昵称: 旅行相关博主1 ✅
   等级: 优质创作者
   简介: 专注旅行分享的优质博主，致力于为大家带来最新最好的内容。
   数据: 粉丝 71,311 | 关注 456 | 笔记 234 | 获赞 45,678
   标签: 旅行, 分享, 种草, 推荐
   链接: https://www.xiaohongshu.com/user/profile/user_1
```

## 📋 注意事项

1. **模拟数据**: 当前版本使用模拟数据进行演示
2. **API集成**: 如需真实数据，请配置小红书API接口
3. **错误处理**: 增强版包含完善的错误处理机制
4. **日志记录**: 所有操作都会记录到日志中
5. **性能优化**: 支持分页查询，避免一次性加载过多数据

## 🚀 后续扩展

### 可以添加的功能
- 实时数据接口对接
- 用户认证和权限控制
- 数据缓存机制
- 更多分析维度
- 导出功能
- 定时任务

### 集成建议
- 配合云开发静态托管使用
- 与前端页面数据展示结合
- 集成到现有的周同学活动网页中

## 📞 技术支持

如果您在使用过程中遇到任何问题，请检查：
1. Python 3.8+ 环境
2. MCP配置文件格式
3. 日志输出信息
4. 网络连接状态

---

🎉 **恭喜！您的增强版小红书MCP服务器已经准备就绪！** 