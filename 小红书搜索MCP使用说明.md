# 小红书搜索 MCP 工具使用说明

## 🎯 功能概述

您现在拥有了一个完整的**小红书搜索MCP工具**，可以在AI对话中直接搜索小红书的内容！

### ✨ 主要功能

- **📝 搜索小红书笔记**: 根据关键词搜索相关的小红书文章内容
- **👥 搜索小红书用户**: 查找相关主题的博主和创作者
- **📊 多种排序方式**: 支持综合、热度、时间等不同排序
- **📄 分页浏览**: 支持翻页查看更多结果

## 🔧 技术架构

### 项目文件结构
```
周同学活动介绍网页版/
├── .mcp.json                    # MCP配置文件
├── xiaohongshu_mcp_simple.py   # 小红书搜索MCP服务器
└── 小红书搜索MCP使用说明.md      # 本说明文档
```

### MCP配置
已在 `.mcp.json` 中配置了两个MCP服务器：
- **cloudbase**: 腾讯云开发工具
- **xiaohongshu-search**: 小红书搜索工具（新增）

## 🚀 使用方法

### 1. 在AI客户端中使用

在支持MCP的AI客户端中，您可以直接使用自然语言请求：

```
帮我搜索关于美食的小红书笔记
查找摄影相关的小红书博主
搜索护肤心得，按热度排序
```

### 2. 可用的搜索工具

#### 📝 search_xiaohongshu_notes
**搜索小红书笔记内容**

参数：
- `keyword` (必需): 搜索关键词
- `page` (可选): 页码，默认为1
- `sort_type` (可选): 排序方式
  - `general` - 综合排序（默认）
  - `hot_desc` - 按热度排序
  - `create_time_desc` - 按时间排序

#### 👥 search_xiaohongshu_users
**搜索小红书用户**

参数：
- `keyword` (必需): 搜索关键词
- `page` (可选): 页码，默认为1

### 3. 使用示例

**搜索美食笔记：**
```
用户：帮我搜索关于火锅的小红书笔记
AI：好的，我来为您搜索关于火锅的小红书笔记...

🔍 小红书笔记搜索结果 - '火锅' (第1页)
==================================================

📝 笔记 1
   标题: 关于火锅的精彩分享 #1
   作者: 博主1
   描述: 这是一篇关于火锅的详细分享，包含实用技巧和心得。
   互动: ❤️ 100 💬 20 🔄 10
   标签: 火锅, 分享, 推荐
   时间: 2024-12-01
   链接: https://www.xiaohongshu.com/explore/note_1
...
```

**搜索用户：**
```
用户：找一些摄影博主
AI：我来为您搜索摄影相关的博主...

👥 小红书用户搜索结果 - '摄影' (第1页)
==================================================

👤 用户 1
   昵称: 摄影相关用户1 ✅ 认证
   简介: 专注摄影分享的博主
   数据: 粉丝 1000 | 关注 100 | 笔记 50
   链接: https://www.xiaohongshu.com/user/profile/user_1
...
```

## 🛠️ 技术特性

### 模拟数据说明
目前使用模拟数据进行演示，包含：
- 真实的小红书链接格式
- 完整的用户信息结构
- 互动数据（点赞、评论、分享）
- 认证标识
- 时间信息

### 扩展接口
如需接入真实的小红书API，可以修改 `xiaohongshu_mcp_simple.py` 中的以下函数：
- `search_xiaohongshu_notes()` - 笔记搜索逻辑
- `search_xiaohongshu_users()` - 用户搜索逻辑

## 📋 支持的AI客户端

此MCP工具兼容所有支持MCP协议的AI客户端：
- **Claude Desktop**
- **Cursor**
- **WindSurf**
- **CodeBuddy**
- **其他支持MCP的AI工具**

## 🔍 高级功能

### 分页搜索
```
搜索美食笔记，第2页的结果
查看更多护肤博主，显示第3页
```

### 指定排序
```
搜索旅行笔记，按热度排序
找最新的数码产品分享
```

### 组合搜索
```
先搜索健身笔记，再找相关的健身博主
对比不同博主的护肤心得
```

## 🚨 注意事项

1. **数据来源**: 当前使用模拟数据，如需真实数据需要配置相应的API
2. **访问频率**: 建议合理控制搜索频率，避免过于频繁的请求
3. **内容准确性**: 模拟数据仅用于功能演示，实际使用时请以真实API为准

## 📞 技术支持

如遇到问题或需要扩展功能，可以：
1. 检查 `.mcp.json` 配置是否正确
2. 确认 `xiaohongshu_mcp_simple.py` 文件是否存在
3. 验证Python环境和依赖是否安装完整

---

🎉 **恭喜！您的小红书搜索MCP工具已经准备就绪，开始享受智能搜索的便利吧！** 