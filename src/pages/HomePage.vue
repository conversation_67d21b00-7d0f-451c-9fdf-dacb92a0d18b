<template>
  <div class="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
    <!-- Hero Section -->
    <section class="relative overflow-hidden">
      <div class="absolute inset-0 bg-black/20"></div>
      <div class="relative container mx-auto px-4 py-20 text-center text-white">
        <div class="animate-bounce mb-8">
          <h1 class="text-6xl font-bold bg-gradient-to-r from-yellow-400 to-pink-500 bg-clip-text text-transparent mb-4">
            周同学 CHOUCHOU
          </h1>
          <p class="text-2xl font-light">厦门地铁主题专列活动</p>
        </div>
        
        <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-8 max-w-4xl mx-auto mb-8">
          <div class="grid md:grid-cols-4 gap-6 text-center">
            <div class="space-y-2">
              <div class="text-3xl font-bold text-yellow-400">{{ remainingDays }}</div>
              <div class="text-sm">剩余天数</div>
            </div>
            <div class="space-y-2">
              <div class="text-3xl font-bold text-pink-400">6+3</div>
              <div class="text-sm">打卡点+快闪店</div>
            </div>
            <div class="space-y-2">
              <div class="text-3xl font-bold text-blue-400">{{ formatNumber(totalCheckins) }}</div>
              <div class="text-sm">累计打卡次数</div>
            </div>
            <div class="space-y-2">
              <div class="text-3xl font-bold text-green-400">{{ formatNumber(onlineUsers) }}</div>
              <div class="text-sm">在线用户</div>
            </div>
          </div>
        </div>

        <div class="space-x-4">
          <button @click="scrollToSection('checkin')" class="btn btn-primary btn-lg">
            🎯 开始打卡
          </button>
          <button @click="scrollToSection('guide')" class="btn btn-secondary btn-lg">
            📖 活动攻略
          </button>
        </div>
      </div>
      
      <!-- Floating Music Notes Animation -->
      <div class="absolute inset-0 pointer-events-none">
        <div v-for="note in musicNotes" :key="note.id" 
             :class="note.class"
             :style="{ left: note.left, animationDelay: note.delay }">
          {{ note.symbol }}
        </div>
      </div>
    </section>

    <!-- Activity Info -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-4">
        <h2 class="text-4xl font-bold text-center mb-12 text-gray-800">活动核心信息</h2>
        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div class="card bg-gradient-to-br from-purple-500 to-pink-500 text-white">
            <div class="card-body text-center">
              <div class="text-3xl mb-2">📅</div>
              <h3 class="card-title text-lg">活动时间</h3>
              <p class="text-sm">即日起至2025年7月20日</p>
            </div>
          </div>
          
          <div class="card bg-gradient-to-br from-blue-500 to-cyan-500 text-white">
            <div class="card-body text-center">
              <div class="text-3xl mb-2">🚇</div>
              <h3 class="card-title text-lg">专列体验</h3>
              <p class="text-sm">厦门地铁1号线主题专列</p>
            </div>
          </div>
          
          <div class="card bg-gradient-to-br from-green-500 to-emerald-500 text-white">
            <div class="card-body text-center">
              <div class="text-3xl mb-2">🎁</div>
              <h3 class="card-title text-lg">积分兑换</h3>
              <p class="text-sm">地铁豆兑换精美周边</p>
            </div>
          </div>
          
          <div class="card bg-gradient-to-br from-orange-500 to-red-500 text-white">
            <div class="card-body text-center">
              <div class="text-3xl mb-2">📱</div>
              <h3 class="card-title text-lg">社交互动</h3>
              <p class="text-sm">小红书抖音双平台联动</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Check-in Points -->
    <section id="checkin" class="py-16 bg-gray-50">
      <div class="container mx-auto px-4">
        <h2 class="text-4xl font-bold text-center mb-12 text-gray-800">六大打卡圣地</h2>
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div v-for="(point, index) in checkinPoints" :key="index" 
               class="card hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 cursor-pointer"
               @click="showPointDetail(point)">
            <figure class="relative overflow-hidden">
              <img :src="point.image" :alt="point.name" class="w-full h-48 object-cover">
              <div class="absolute top-4 right-4 badge badge-primary">{{ point.badge }}</div>
              <div class="absolute bottom-4 left-4 text-white">
                <div class="text-sm opacity-80">{{ point.subway }}</div>
              </div>
            </figure>
            <div class="card-body">
              <h3 class="card-title">{{ point.name }}</h3>
              <p class="text-gray-600 text-sm">{{ point.description }}</p>
              <div class="card-actions justify-between items-center mt-4">
                <div class="flex space-x-1">
                  <div v-for="i in 5" :key="i" 
                       :class="i <= point.rating ? 'text-yellow-400' : 'text-gray-300'">
                    ⭐
                  </div>
                </div>
                <div class="text-sm text-gray-500">{{ point.tips }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Points System -->
    <section class="py-16 bg-blue-50">
      <div class="container mx-auto px-4">
        <h2 class="text-4xl font-bold text-center mb-12 text-gray-800">积分体系攻略</h2>
        
        <!-- Points Earning -->
        <div class="grid md:grid-cols-2 gap-12 mb-16">
          <div class="space-y-6">
            <h3 class="text-2xl font-bold text-blue-800 mb-6">🔮 获取地铁豆</h3>
            <div v-for="task in pointTasks" :key="task.name" 
                 class="bg-white rounded-lg p-4 shadow-md hover:shadow-lg transition-shadow">
              <div class="flex justify-between items-center">
                <div class="flex items-center space-x-3">
                  <div class="text-2xl">{{ task.icon }}</div>
                  <div>
                    <div class="font-semibold">{{ task.name }}</div>
                    <div class="text-sm text-gray-600">{{ task.description }}</div>
                  </div>
                </div>
                <div class="text-right">
                  <div class="text-lg font-bold text-blue-600">+{{ task.points }}</div>
                  <div class="text-xs text-gray-500">{{ task.limit }}</div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="space-y-6">
            <h3 class="text-2xl font-bold text-pink-800 mb-6">🎁 兑换奖励</h3>
            <div v-for="reward in rewards" :key="reward.name" 
                 class="bg-white rounded-lg p-4 shadow-md hover:shadow-lg transition-shadow">
              <div class="flex justify-between items-center">
                <div class="flex items-center space-x-3">
                  <div class="text-2xl">{{ reward.icon }}</div>
                  <div>
                    <div class="font-semibold">{{ reward.name }}</div>
                    <div class="text-sm text-gray-600">{{ reward.description }}</div>
                  </div>
                </div>
                <div class="text-right">
                  <div class="text-lg font-bold text-pink-600">{{ reward.cost }}</div>
                  <div class="text-xs text-gray-500">{{ reward.stock }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Social Media Integration -->
    <section class="py-16 bg-gradient-to-r from-pink-500 to-purple-600 text-white">
      <div class="container mx-auto px-4 text-center">
        <h2 class="text-4xl font-bold mb-8">社交媒体联动</h2>
        <div class="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          
          <!-- XiaoHongShu -->
          <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-8">
            <div class="text-6xl mb-4">📱</div>
            <h3 class="text-2xl font-bold mb-4">小红书挑战</h3>
            <div class="space-y-3 text-left">
              <div class="flex items-center space-x-2">
                <span class="text-yellow-400">✨</span>
                <span>发布笔记带话题 #周同学CHOUCHOU厦门站#</span>
              </div>
              <div class="flex items-center space-x-2">
                <span class="text-yellow-400">❤️</span>
                <span>集赞88个→宠粉礼包</span>
              </div>
              <div class="flex items-center space-x-2">
                <span class="text-yellow-400">🎁</span>
                <span>集赞118个→城市限定冰箱贴</span>
              </div>
            </div>
            <button @click="showSearchModal = true" class="btn btn-warning mt-4">
              搜索相关内容
            </button>
          </div>
          
          <!-- Douyin -->
          <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-8">
            <div class="text-6xl mb-4">🎵</div>
            <h3 class="text-2xl font-bold mb-4">抖音AR互动</h3>
            <div class="space-y-3 text-left">
              <div class="flex items-center space-x-2">
                <span class="text-blue-400">🔮</span>
                <span>扫描快闪店雕塑生成特效</span>
              </div>
              <div class="flex items-center space-x-2">
                <span class="text-blue-400">🏆</span>
                <span>话题 #周同学AR互动# 获500积分</span>
              </div>
              <div class="flex items-center space-x-2">
                <span class="text-blue-400">🎤</span>
                <span>每日19:00直播抽奖演唱会门票</span>
              </div>
            </div>
            <button class="btn btn-info mt-4">观看直播</button>
          </div>
        </div>
      </div>
    </section>

    <!-- Guide Section -->
    <section id="guide" class="py-16 bg-white">
      <div class="container mx-auto px-4">
        <h2 class="text-4xl font-bold text-center mb-12 text-gray-800">实用攻略</h2>
        
        <!-- Route Planning -->
        <div class="max-w-4xl mx-auto">
          <div class="tabs tabs-boxed bg-gray-100 mb-8">
            <a v-for="(route, index) in routes" :key="index"
               :class="['tab', { 'tab-active': activeRoute === index }]"
               @click="activeRoute = index">
              {{ route.name }}
            </a>
          </div>
          
          <div class="bg-gray-50 rounded-lg p-8">
            <h3 class="text-2xl font-bold mb-6 text-blue-800">{{ routes[activeRoute].name }}</h3>
            <div class="timeline timeline-vertical">
              <div v-for="(step, index) in routes[activeRoute].steps" :key="index" class="timeline-item">
                <div class="timeline-middle">
                  <div class="timeline-marker bg-primary text-white">{{ index + 1 }}</div>
                </div>
                <div class="timeline-content">
                  <div class="font-semibold">{{ step.time }}</div>
                  <div class="text-lg">{{ step.location }}</div>
                  <div class="text-gray-600">{{ step.description }}</div>
                  <div class="text-sm text-blue-600 mt-1">{{ step.tips }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Real-time Updates -->
    <section class="py-16 bg-gray-100">
      <div class="container mx-auto px-4">
        <h2 class="text-4xl font-bold text-center mb-12 text-gray-800">实时数据中心</h2>
        <div class="grid lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
          
          <!-- 实时统计组件 -->
          <div class="lg:col-span-1">
            <RealtimeStats />
          </div>
          
          <!-- 内容流组件 -->
          <div class="lg:col-span-2">
            <ContentFeed />
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
      <div class="container mx-auto px-4 text-center">
        <div class="mb-8">
          <h3 class="text-2xl font-bold mb-4">关注官方获取最新资讯</h3>
          <div class="flex justify-center space-x-4">
            <button class="btn btn-circle btn-outline btn-primary">
              微信
            </button>
            <button class="btn btn-circle btn-outline btn-secondary">
              微博
            </button>
            <button class="btn btn-circle btn-outline btn-accent">
              抖音
            </button>
          </div>
        </div>
        <div class="text-gray-400 text-sm">
          <p>活动时间：即日起至2025年7月20日</p>
          <p>主办方：厦门文旅 × 厦门地铁</p>
          <p class="mt-4">让我们一起在音乐的海洋中遇见最美的厦门 🎵</p>
        </div>
      </div>
    </footer>

    <!-- Point Detail Modal -->
    <div v-if="selectedPoint" class="modal modal-open">
      <div class="modal-box max-w-2xl">
        <h3 class="font-bold text-lg">{{ selectedPoint.name }}</h3>
        <img :src="selectedPoint.image" :alt="selectedPoint.name" class="w-full h-64 object-cover rounded-lg my-4">
        <p class="py-4">{{ selectedPoint.fullDescription }}</p>
        
        <div class="grid grid-cols-2 gap-4 my-4">
          <div class="bg-blue-50 p-3 rounded">
            <div class="font-semibold text-blue-800">最佳拍摄时间</div>
            <div class="text-sm">{{ selectedPoint.bestTime }}</div>
          </div>
          <div class="bg-green-50 p-3 rounded">
            <div class="font-semibold text-green-800">推荐装备</div>
            <div class="text-sm">{{ selectedPoint.equipment }}</div>
          </div>
        </div>
        
        <div class="modal-action">
          <button class="btn btn-primary">开始导航</button>
          <button class="btn" @click="selectedPoint = null">关闭</button>
        </div>
      </div>
    </div>

    <!-- Search Modal -->
    <div v-if="showSearchModal" class="modal modal-open">
      <div class="modal-box max-w-6xl max-h-[90vh] overflow-y-auto">
        <button
          @click="showSearchModal = false"
          class="btn btn-sm btn-circle absolute right-2 top-2 z-10"
        >
          ✕
        </button>
        
        <XiaoHongShuSearch />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import XiaoHongShuSearch from '../components/XiaoHongShuSearch.vue'
import RealtimeStats from '../components/RealtimeStats.vue'
import ContentFeed from '../components/ContentFeed.vue'
import realDataService from '../services/realDataService.js'

// Reactive data
const remainingDays = ref(calculateRemainingDays())
const totalCheckins = ref(18500)
const onlineUsers = ref(2800)
const selectedPoint = ref(null)
const activeRoute = ref(0)
const lastUpdate = ref(new Date().toLocaleTimeString())
const showSearchModal = ref(false)
const realtimeData = ref({})

// Music notes animation data
const musicNotes = ref([
  { id: 1, symbol: '♪', class: 'absolute text-4xl text-yellow-400 animate-pulse floating-note-1', left: '10%', delay: '0s' },
  { id: 2, symbol: '♫', class: 'absolute text-3xl text-pink-400 animate-bounce floating-note-2', left: '80%', delay: '1s' },
  { id: 3, symbol: '♬', class: 'absolute text-5xl text-blue-400 animate-ping floating-note-3', left: '60%', delay: '2s' },
  { id: 4, symbol: '♩', class: 'absolute text-2xl text-green-400 animate-pulse floating-note-4', left: '30%', delay: '3s' },
])

// Check-in points data
const checkinPoints = ref([
  {
    name: '十里长堤5.5米大娃',
    description: '草坪音乐会与巨型雕塑的完美结合',
    image: 'https://images.unsplash.com/photo-1514924013411-cbf25faa35bb?w=400',
    badge: '人气王',
    subway: '地铁1号线',
    rating: 5,
    tips: '傍晚最佳',
    bestTime: '傍晚6-7点，利用落日逆光拍摄',
    equipment: '广角镜头、浅色长裙',
    fullDescription: '5.5米高的周同学巨型雕塑矗立在十里长堤，配合草坪音乐会营造浪漫氛围。傍晚时分，落日余晖洒向雕塑，是拍摄的黄金时间。'
  },
  {
    name: '海上世界音乐台阶',
    description: 'AR互动与《晴天》旋律的奇妙体验',
    image: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400',
    badge: '科技感',
    subway: '地铁2号线',
    rating: 5,
    tips: '夜间最美',
    bestTime: '夜间开启慢动作拍摄踩踏效果',
    equipment: '手机慢动作模式、三脚架',
    fullDescription: '踩踏台阶触发《晴天》旋律，夜晚灯光效果更佳。通过抖音扫描雕塑可生成周同学弹奏钢琴的AR特效。'
  },
  {
    name: 'SM城市广场7米雕塑',
    description: '购物天堂里的音乐圣地',
    image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400',
    badge: '便利之选',
    subway: '地铁1号线',
    rating: 4,
    tips: '人流较少',
    bestTime: '上午10点或下午3点人流较少',
    equipment: '仰角拍摄突出雕塑高度',
    fullDescription: '7米高雕塑位于SM城市广场，周边配套完善。消费满199元可抽扭蛋机，100%中奖获得签名海报或演唱会抵扣券。'
  },
  {
    name: '中山路骑楼街区',
    description: '闽南文化与现代音乐的碰撞',
    image: 'https://images.unsplash.com/photo-1545565597-6c0fefe2c9c4?w=400',
    badge: '文化底蕴',
    subway: '地铁1号线',
    rating: 4,
    tips: '文创丰富',
    bestTime: '全天候，建议避开用餐高峰',
    equipment: '复古滤镜、闽南元素道具',
    fullDescription: '骑楼建筑承载百年历史，《爱在鹭岛 诗笺漫游》明信片套装可用紫外线灯解锁隐藏歌词，是独特的文创体验。'
  },
  {
    name: '鼓浪屿IP元素',
    description: '钢琴之岛的音乐传承',
    image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400',
    badge: '经典',
    subway: '轮渡+步行',
    rating: 5,
    tips: '需要门票',
    bestTime: '上午购买联票避开人流高峰',
    equipment: '长焦镜头、防晒用品',
    fullDescription: '菽庄花园钢琴码头与周同学元素完美融合，50元联票含五大景点，在龙头路邮政支局可加盖专属纪念印章。'
  },
  {
    name: '荻花洲艺术装置',
    description: '现代艺术与音乐美学的结合',
    image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400',
    badge: '艺术感',
    subway: '地铁3号线',
    rating: 4,
    tips: '避开拥堵',
    bestTime: '白天镜面反射，傍晚剪影效果',
    equipment: '长焦镜头、偏振镜',
    fullDescription: '利用镜面反射效果拍摄对称构图，傍晚时分可捕捉雕塑与落日的美丽剪影，地铁3号线直达避免热门点位排队。'
  }
])

// Points system data
const pointTasks = ref([
  { name: '每日签到', description: '连续签到获得额外奖励', icon: '📅', points: 50, limit: '每日1次' },
  { name: '扫码乘车', description: '使用厦门地铁小程序乘车', icon: '🚇', points: 100, limit: '每日不限' },
  { name: '邀请好友', description: '好友完成注册即可获得', icon: '👥', points: 100, limit: '上限500豆/日' },
  { name: '打卡点扫码', description: '六大打卡点扫描二维码', icon: '📍', points: 200, limit: '每日2次' },
  { name: 'NPC互动', description: '快闪店内与NPC对话', icon: '🤖', points: '50-100', limit: '7月11-13日' }
])

const rewards = ref([
  { name: '星际光轮篮球', description: '限量版周同学主题篮球', icon: '🏀', cost: '2000豆', stock: '每日5个' },
  { name: '9.9元唱游卡', description: '8次乘车+商圈餐饮券', icon: '🎫', cost: '优惠价', stock: '充足' },
  { name: '快闪店满减券', description: '满99元减20元优惠券', icon: '🎟️', cost: '300豆', stock: '充足' },
  { name: 'P+R停车券', description: 'Park+Ride停车抵扣', icon: '🅿️', cost: '积分兑换', stock: '有限' }
])

// Route planning data
const routes = ref([
  {
    name: '经典一日游',
    steps: [
      { time: '09:00', location: 'SM城市广场', description: '7米雕塑打卡+快闪店购物', tips: '消费满199元抽扭蛋机' },
      { time: '11:00', location: '中山路骑楼', description: '文创购买+闽南文化体验', tips: '购买明信片套装纪念' },
      { time: '14:00', location: '海上世界', description: '音乐台阶+AR互动体验', tips: '夜晚效果更佳，可晚点再来' },
      { time: '16:00', location: '鼓浪屿', description: '钢琴码头+五大景点游览', tips: '购买50元联票性价比高' },
      { time: '18:00', location: '十里长堤', description: '草坪音乐会+落日拍摄', tips: '傍晚光线最佳，准备广角镜头' },
      { time: '20:00', location: '荻花洲', description: '夜景拍摄+艺术装置', tips: '地铁3号线直达避免拥堵' }
    ]
  },
  {
    name: '快闪购物游',
    steps: [
      { time: '10:00', location: 'SM城市广场快闪店', description: '抢购限量篮球+扭蛋体验', tips: '开门即到，避免售罄' },
      { time: '12:00', location: '海上世界快闪店', description: '潮汐之眼冰箱贴+AR拍摄', tips: '15:00有补货，可等待' },
      { time: '15:00', location: '中山路快闪店', description: '文创套装+邮政印章', tips: '龙头路邮政支局加盖印章' },
      { time: '17:00', location: '各打卡点', description: '完成集章任务', tips: '6枚印章+消费满199元获徽章' }
    ]
  },
  {
    name: '积分刷豆游',
    steps: [
      { time: '全天', location: '厦门地铁', description: '扫码乘车+邀请好友', tips: '每次乘车100豆，邀请奖励丰厚' },
      { time: '定点', location: '打卡点', description: '每日2次扫码任务', tips: '每次200豆，选择人少的点位' },
      { time: '7.11-7.13', location: '快闪店', description: 'NPC互动获得额外积分', tips: '限时活动，不要错过' }
    ]
  }
])

// Real-time data
const recentWinners = ref([
  { id: 1, name: '用户***1', prize: '篮球' },
  { id: 2, name: '用户***2', prize: '海报' },
  { id: 3, name: '用户***3', prize: '冰箱贴' },
  { id: 4, name: '用户***4', prize: '徽章' }
])

const todaySongs = ref([
  { time: '10:00', name: '晴天' },
  { time: '14:00', name: '稻香' },
  { time: '16:00', name: '青花瓷' },
  { time: '19:00', name: '七里香' }
])

// Methods
function calculateRemainingDays() {
  const endDate = new Date('2025-07-20')
  const today = new Date()
  const diffTime = endDate - today
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
}

function scrollToSection(sectionId) {
  document.getElementById(sectionId)?.scrollIntoView({ behavior: 'smooth' })
}

function showPointDetail(point) {
  selectedPoint.value = point
}

function formatNumber(num) {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toLocaleString()
}

async function updateRealtimeData() {
  try {
    const data = await realDataService.getRealtimeData()
    totalCheckins.value = data.totalCheckins
    onlineUsers.value = data.onlineUsers
    realtimeData.value = data
  } catch (error) {
    console.error('更新实时数据失败:', error)
  }
}



// Update data periodically
onMounted(async () => {
  // 初始加载实时数据
  await updateRealtimeData()
  
  // 每30秒更新一次数据
  setInterval(async () => {
    await updateRealtimeData()
    lastUpdate.value = new Date().toLocaleTimeString()
  }, 30000)
  
  // 每5秒小幅更新计数器
  setInterval(() => {
    totalCheckins.value += Math.floor(Math.random() * 3) + 1
    onlineUsers.value += Math.floor(Math.random() * 20) - 10
    if (onlineUsers.value < 2000) onlineUsers.value = 2000 + Math.floor(Math.random() * 1000)
  }, 5000)
})
</script>

<style scoped>
.floating-note-1 {
  animation: float1 4s ease-in-out infinite;
}

.floating-note-2 {
  animation: float2 5s ease-in-out infinite;
}

.floating-note-3 {
  animation: float3 3s ease-in-out infinite;
}

.floating-note-4 {
  animation: float4 6s ease-in-out infinite;
}

@keyframes float1 {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes float2 {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-30px) rotate(-180deg); }
}

@keyframes float3 {
  0%, 100% { transform: translateY(0px) scale(1); }
  50% { transform: translateY(-25px) scale(1.2); }
}

@keyframes float4 {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-15px) rotate(120deg); }
  66% { transform: translateY(-10px) rotate(240deg); }
}

.timeline-vertical .timeline-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 50%;
  top: 3rem;
  height: calc(100% - 3rem);
  width: 2px;
  background-color: #e5e7eb;
  transform: translateX(-50%);
}

.timeline-marker {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  z-index: 10;
  position: relative;
}
</style> 