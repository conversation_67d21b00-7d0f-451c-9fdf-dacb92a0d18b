<template>
  <div class="min-h-screen bg-white">
    <!-- 顶部导航栏 -->
    <nav class="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-lg border-b border-gray-200">
      <div class="container mx-auto px-6 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="w-10 h-10 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full flex items-center justify-center">
              <span class="text-white font-bold text-lg">周</span>
            </div>
            <div>
              <h1 class="text-xl font-bold text-gray-800">周同学CHOUCHOU厦门站</h1>
              <p class="text-sm text-gray-600">官方活动网站</p>
            </div>
          </div>
          <div class="hidden md:flex items-center space-x-8">
            <a href="#overview" class="text-gray-700 hover:text-pink-500 transition-colors">活动概览</a>
            <a href="#checkin" class="text-gray-700 hover:text-pink-500 transition-colors">打卡地点</a>
            <a href="#social" class="text-gray-700 hover:text-pink-500 transition-colors">社交动态</a>
            <a href="#guide" class="text-gray-700 hover:text-pink-500 transition-colors">参与指南</a>
          </div>
          <button class="bg-gradient-to-r from-pink-500 to-purple-500 text-white px-6 py-2 rounded-full hover:shadow-lg transition-all">
            立即参与
          </button>
        </div>
      </div>
    </nav>

    <!-- Hero Section - 重新设计为更专业的展示 -->
    <section id="overview" class="relative pt-24 pb-20 bg-gradient-to-br from-pink-50 via-purple-50 to-blue-50 overflow-hidden">
      <!-- 背景装饰 -->
      <div class="absolute inset-0">
        <div class="absolute top-20 left-10 w-72 h-72 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
        <div class="absolute top-40 right-10 w-72 h-72 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
        <div class="absolute bottom-20 left-20 w-72 h-72 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
      </div>

      <div class="relative container mx-auto px-6">
        <div class="max-w-6xl mx-auto">
          <!-- 主标题区域 -->
          <div class="text-center mb-16">
            <div class="inline-flex items-center bg-white/80 backdrop-blur-sm rounded-full px-6 py-3 mb-8 shadow-lg">
              <span class="w-3 h-3 bg-green-500 rounded-full mr-3 animate-pulse"></span>
              <span class="text-sm font-medium text-gray-700">活动进行中 · 已有 {{ formatNumber(totalCheckins) }} 人参与</span>
            </div>

            <h1 class="text-5xl md:text-7xl font-bold mb-6">
              <span class="bg-gradient-to-r from-pink-600 via-purple-600 to-blue-600 bg-clip-text text-transparent">
                周同学 CHOUCHOU
              </span>
            </h1>
            <h2 class="text-2xl md:text-4xl font-light text-gray-700 mb-6">厦门地铁主题专列活动</h2>
            <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              音乐与城市的完美融合，打造独一无二的沉浸式文旅体验。
              <br class="hidden md:block">
              六大打卡圣地 × 三大快闪店 × 无限音乐回忆
            </p>
          </div>

          <!-- 核心数据展示 -->
          <div class="bg-white/15 backdrop-blur-xl rounded-3xl p-8 mb-12 border border-white/20">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
              <div class="text-center">
                <div class="text-4xl md:text-5xl font-bold text-yellow-400 mb-2">{{ remainingDays }}</div>
                <div class="text-sm md:text-base text-gray-200">剩余天数</div>
              </div>
              <div class="text-center">
                <div class="text-4xl md:text-5xl font-bold text-pink-400 mb-2">6+3</div>
                <div class="text-sm md:text-base text-gray-200">打卡点+快闪店</div>
              </div>
              <div class="text-center">
                <div class="text-4xl md:text-5xl font-bold text-blue-400 mb-2">{{ formatNumber(totalCheckins) }}</div>
                <div class="text-sm md:text-base text-gray-200">累计打卡次数</div>
              </div>
              <div class="text-center">
                <div class="text-4xl md:text-5xl font-bold text-green-400 mb-2">{{ formatNumber(onlineUsers) }}</div>
                <div class="text-sm md:text-base text-gray-200">在线用户</div>
              </div>
            </div>
          </div>

          <!-- 行动按钮 -->
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <button @click="scrollToSection('checkin')"
                    class="bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white font-bold py-4 px-8 rounded-full text-lg transition-all duration-300 transform hover:scale-105 shadow-lg">
              🎯 开始打卡之旅
            </button>
            <button @click="scrollToSection('guide')"
                    class="bg-white/20 hover:bg-white/30 backdrop-blur-lg text-white font-bold py-4 px-8 rounded-full text-lg transition-all duration-300 border border-white/30">
              📖 查看攻略
            </button>
          </div>
        </div>
      </div>

      <!-- Floating Music Notes Animation -->
      <div class="absolute inset-0 pointer-events-none">
        <div v-for="note in musicNotes" :key="note.id"
             :class="note.class"
             :style="{ left: note.left, animationDelay: note.delay }">
          {{ note.symbol }}
        </div>
      </div>
    </section>

    <!-- Activity Info -->
    <section class="py-20 bg-gray-50">
      <div class="container mx-auto px-6">
        <div class="text-center mb-16">
          <h2 class="text-4xl md:text-5xl font-bold text-gray-800 mb-4">活动亮点</h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            音乐与城市的完美融合，打造独一无二的沉浸式体验
          </p>
        </div>

        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-7xl mx-auto">
          <div class="group">
            <div class="bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl p-8 text-white text-center transform transition-all duration-300 hover:scale-105 hover:shadow-2xl">
              <div class="text-5xl mb-4 group-hover:animate-bounce">📅</div>
              <h3 class="text-xl font-bold mb-3">活动时间</h3>
              <p class="text-purple-100">即日起至2025年7月20日</p>
              <div class="mt-4 text-sm bg-white/20 rounded-full px-4 py-2 inline-block">
                限时体验
              </div>
            </div>
          </div>

          <div class="group">
            <div class="bg-gradient-to-br from-blue-500 to-cyan-500 rounded-2xl p-8 text-white text-center transform transition-all duration-300 hover:scale-105 hover:shadow-2xl">
              <div class="text-5xl mb-4 group-hover:animate-bounce">🚇</div>
              <h3 class="text-xl font-bold mb-3">专列体验</h3>
              <p class="text-blue-100">厦门地铁1号线主题专列</p>
              <div class="mt-4 text-sm bg-white/20 rounded-full px-4 py-2 inline-block">
                沉浸体验
              </div>
            </div>
          </div>

          <div class="group">
            <div class="bg-gradient-to-br from-green-500 to-emerald-500 rounded-2xl p-8 text-white text-center transform transition-all duration-300 hover:scale-105 hover:shadow-2xl">
              <div class="text-5xl mb-4 group-hover:animate-bounce">🎁</div>
              <h3 class="text-xl font-bold mb-3">积分兑换</h3>
              <p class="text-green-100">地铁豆兑换精美周边</p>
              <div class="mt-4 text-sm bg-white/20 rounded-full px-4 py-2 inline-block">
                丰富奖励
              </div>
            </div>
          </div>

          <div class="group">
            <div class="bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl p-8 text-white text-center transform transition-all duration-300 hover:scale-105 hover:shadow-2xl">
              <div class="text-5xl mb-4 group-hover:animate-bounce">📱</div>
              <h3 class="text-xl font-bold mb-3">社交互动</h3>
              <p class="text-orange-100">小红书抖音双平台联动</p>
              <div class="mt-4 text-sm bg-white/20 rounded-full px-4 py-2 inline-block">
                全民参与
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Check-in Points -->
    <section id="checkin" class="py-20 bg-white">
      <div class="container mx-auto px-6">
        <div class="text-center mb-16">
          <h2 class="text-4xl md:text-5xl font-bold text-gray-800 mb-4">六大打卡圣地</h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            每个地点都有独特的音乐体验，收集全部印章解锁专属奖励
          </p>
        </div>

        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
          <div v-for="(point, index) in checkinPoints" :key="index"
               class="group bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 cursor-pointer border border-gray-100"
               @click="showPointDetail(point)">
            <div class="relative overflow-hidden">
              <img :src="point.image" :alt="point.name" class="w-full h-56 object-cover group-hover:scale-110 transition-transform duration-500">
              <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
              <div class="absolute top-4 right-4">
                <span class="bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs font-bold px-3 py-1 rounded-full">
                  {{ point.badge }}
                </span>
              </div>
              <div class="absolute bottom-4 left-4 text-white">
                <div class="flex items-center space-x-2">
                  <span class="text-sm bg-white/20 backdrop-blur-sm rounded-full px-3 py-1">
                    {{ point.subway }}
                  </span>
                </div>
              </div>
            </div>

            <div class="p-6">
              <h3 class="text-xl font-bold text-gray-800 mb-2 group-hover:text-blue-600 transition-colors">
                {{ point.name }}
              </h3>
              <p class="text-gray-600 text-sm mb-4 leading-relaxed">{{ point.description }}</p>

              <div class="flex justify-between items-center">
                <div class="flex space-x-1">
                  <div v-for="i in 5" :key="i"
                       :class="i <= point.rating ? 'text-yellow-400' : 'text-gray-300'"
                       class="text-lg">
                    ⭐
                  </div>
                </div>
                <div class="text-sm text-blue-600 font-medium bg-blue-50 px-3 py-1 rounded-full">
                  {{ point.tips }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Points System -->
    <section class="py-20 bg-gradient-to-br from-blue-50 to-indigo-100">
      <div class="container mx-auto px-6">
        <div class="text-center mb-16">
          <h2 class="text-4xl md:text-5xl font-bold text-gray-800 mb-4">积分体系攻略</h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            完成任务获取地铁豆，兑换专属周边和优惠券
          </p>
        </div>

        <div class="max-w-7xl mx-auto">
          <!-- Points Earning -->
          <div class="grid lg:grid-cols-2 gap-12">
            <!-- 获取地铁豆 -->
            <div class="bg-white rounded-3xl p-8 shadow-xl">
              <div class="flex items-center mb-8">
                <div class="text-4xl mr-4">🔮</div>
                <h3 class="text-3xl font-bold text-blue-800">获取地铁豆</h3>
              </div>

              <div class="space-y-4">
                <div v-for="task in pointTasks" :key="task.name"
                     class="group bg-gradient-to-r from-blue-50 to-cyan-50 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 border border-blue-100">
                  <div class="flex justify-between items-center">
                    <div class="flex items-center space-x-4">
                      <div class="text-3xl group-hover:scale-110 transition-transform">{{ task.icon }}</div>
                      <div>
                        <div class="font-bold text-lg text-gray-800">{{ task.name }}</div>
                        <div class="text-sm text-gray-600 mt-1">{{ task.description }}</div>
                      </div>
                    </div>
                    <div class="text-right">
                      <div class="text-2xl font-bold text-blue-600">+{{ task.points }}</div>
                      <div class="text-xs text-gray-500 bg-white rounded-full px-3 py-1 mt-1">{{ task.limit }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 兑换奖励 -->
            <div class="bg-white rounded-3xl p-8 shadow-xl">
              <div class="flex items-center mb-8">
                <div class="text-4xl mr-4">🎁</div>
                <h3 class="text-3xl font-bold text-pink-800">兑换奖励</h3>
              </div>

              <div class="space-y-4">
                <div v-for="reward in rewards" :key="reward.name"
                     class="group bg-gradient-to-r from-pink-50 to-rose-50 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 border border-pink-100">
                  <div class="flex justify-between items-center">
                    <div class="flex items-center space-x-4">
                      <div class="text-3xl group-hover:scale-110 transition-transform">{{ reward.icon }}</div>
                      <div>
                        <div class="font-bold text-lg text-gray-800">{{ reward.name }}</div>
                        <div class="text-sm text-gray-600 mt-1">{{ reward.description }}</div>
                      </div>
                    </div>
                    <div class="text-right">
                      <div class="text-2xl font-bold text-pink-600">{{ reward.cost }}</div>
                      <div class="text-xs text-gray-500 bg-white rounded-full px-3 py-1 mt-1">{{ reward.stock }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Social Media Integration -->
    <section class="py-20 bg-gradient-to-br from-pink-500 via-purple-600 to-indigo-700 text-white relative overflow-hidden">
      <div class="absolute inset-0 bg-black/20"></div>
      <div class="relative container mx-auto px-6">
        <div class="text-center mb-16">
          <h2 class="text-4xl md:text-5xl font-bold mb-4">社交媒体联动</h2>
          <p class="text-xl text-pink-100 max-w-3xl mx-auto">
            分享你的音乐之旅，获得更多奖励和互动体验
          </p>
        </div>

        <div class="grid lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
          <!-- XiaoHongShu -->
          <div class="group">
            <div class="bg-white/15 backdrop-blur-xl rounded-3xl p-8 border border-white/20 hover:bg-white/20 transition-all duration-300">
              <div class="text-center mb-6">
                <div class="text-7xl mb-4 group-hover:animate-pulse">📱</div>
                <h3 class="text-3xl font-bold mb-2">小红书挑战</h3>
                <p class="text-pink-100">记录你的音乐打卡之旅</p>
              </div>

              <div class="space-y-4 mb-8">
                <div class="bg-white/10 rounded-2xl p-4 backdrop-blur-sm">
                  <div class="flex items-center space-x-3">
                    <span class="text-2xl">✨</span>
                    <div>
                      <div class="font-semibold">发布笔记</div>
                      <div class="text-sm text-pink-100">带话题 #周同学CHOUCHOU厦门站#</div>
                    </div>
                  </div>
                </div>

                <div class="bg-white/10 rounded-2xl p-4 backdrop-blur-sm">
                  <div class="flex items-center space-x-3">
                    <span class="text-2xl">❤️</span>
                    <div>
                      <div class="font-semibold">集赞88个</div>
                      <div class="text-sm text-pink-100">获得宠粉礼包</div>
                    </div>
                  </div>
                </div>

                <div class="bg-white/10 rounded-2xl p-4 backdrop-blur-sm">
                  <div class="flex items-center space-x-3">
                    <span class="text-2xl">🎁</span>
                    <div>
                      <div class="font-semibold">集赞118个</div>
                      <div class="text-sm text-pink-100">获得城市限定冰箱贴</div>
                    </div>
                  </div>
                </div>
              </div>

              <button @click="showSearchModal = true"
                      class="w-full bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white font-bold py-4 px-6 rounded-2xl transition-all duration-300 transform hover:scale-105">
                🔍 搜索相关内容
              </button>
            </div>
          </div>

          <!-- Douyin -->
          <div class="group">
            <div class="bg-white/15 backdrop-blur-xl rounded-3xl p-8 border border-white/20 hover:bg-white/20 transition-all duration-300">
              <div class="text-center mb-6">
                <div class="text-7xl mb-4 group-hover:animate-pulse">🎵</div>
                <h3 class="text-3xl font-bold mb-2">抖音AR互动</h3>
                <p class="text-purple-100">体验音乐AR特效</p>
              </div>

              <div class="space-y-4 mb-8">
                <div class="bg-white/10 rounded-2xl p-4 backdrop-blur-sm">
                  <div class="flex items-center space-x-3">
                    <span class="text-2xl">🔮</span>
                    <div>
                      <div class="font-semibold">AR特效</div>
                      <div class="text-sm text-purple-100">扫描快闪店雕塑生成特效</div>
                    </div>
                  </div>
                </div>

                <div class="bg-white/10 rounded-2xl p-4 backdrop-blur-sm">
                  <div class="flex items-center space-x-3">
                    <span class="text-2xl">🏆</span>
                    <div>
                      <div class="font-semibold">话题挑战</div>
                      <div class="text-sm text-purple-100">#周同学AR互动# 获500积分</div>
                    </div>
                  </div>
                </div>

                <div class="bg-white/10 rounded-2xl p-4 backdrop-blur-sm">
                  <div class="flex items-center space-x-3">
                    <span class="text-2xl">🎤</span>
                    <div>
                      <div class="font-semibold">直播抽奖</div>
                      <div class="text-sm text-purple-100">每日19:00抽奖演唱会门票</div>
                    </div>
                  </div>
                </div>
              </div>

              <button class="w-full bg-gradient-to-r from-blue-400 to-cyan-500 hover:from-blue-500 hover:to-cyan-600 text-white font-bold py-4 px-6 rounded-2xl transition-all duration-300 transform hover:scale-105">
                📺 观看直播
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Guide Section -->
    <section id="guide" class="py-20 bg-gray-50">
      <div class="container mx-auto px-6">
        <div class="text-center mb-16">
          <h2 class="text-4xl md:text-5xl font-bold text-gray-800 mb-4">实用攻略</h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            三种游玩路线，满足不同需求的音乐之旅
          </p>
        </div>

        <!-- Route Planning -->
        <div class="max-w-6xl mx-auto">
          <!-- 路线选择器 -->
          <div class="flex flex-wrap justify-center gap-4 mb-12">
            <button v-for="(route, index) in routes" :key="index"
                    :class="[
                      'px-8 py-4 rounded-2xl font-bold text-lg transition-all duration-300 transform hover:scale-105',
                      activeRoute === index
                        ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg'
                        : 'bg-white text-gray-700 hover:bg-gray-100 shadow-md border border-gray-200'
                    ]"
                    @click="activeRoute = index">
              {{ route.name }}
            </button>
          </div>

          <!-- 路线详情 -->
          <div class="bg-white rounded-3xl p-8 shadow-xl border border-gray-100">
            <div class="text-center mb-8">
              <h3 class="text-3xl font-bold text-gray-800 mb-2">{{ routes[activeRoute].name }}</h3>
              <p class="text-gray-600">精心规划的音乐打卡路线</p>
            </div>

            <div class="space-y-6">
              <div v-for="(step, index) in routes[activeRoute].steps" :key="index"
                   class="flex items-start space-x-6 p-6 bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl hover:shadow-md transition-shadow">
                <!-- 时间轴标记 -->
                <div class="flex-shrink-0">
                  <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full flex items-center justify-center font-bold text-lg shadow-lg">
                    {{ index + 1 }}
                  </div>
                </div>

                <!-- 内容 -->
                <div class="flex-1">
                  <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-3">
                    <div class="font-bold text-xl text-gray-800">{{ step.location }}</div>
                    <div class="text-lg font-semibold text-blue-600 bg-blue-100 px-4 py-1 rounded-full">
                      {{ step.time }}
                    </div>
                  </div>
                  <p class="text-gray-700 mb-3 leading-relaxed">{{ step.description }}</p>
                  <div class="bg-yellow-50 border-l-4 border-yellow-400 p-3 rounded-r-lg">
                    <div class="flex items-center">
                      <span class="text-yellow-600 mr-2">💡</span>
                      <span class="text-sm text-yellow-800 font-medium">{{ step.tips }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Real-time Updates -->
    <section class="py-20 bg-white">
      <div class="container mx-auto px-6">
        <div class="text-center mb-16">
          <h2 class="text-4xl md:text-5xl font-bold text-gray-800 mb-4">实时数据中心</h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            实时追踪活动数据，发现最新的音乐内容和用户动态
          </p>
        </div>

        <div class="grid lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
          <!-- 实时统计组件 -->
          <div class="lg:col-span-1">
            <div class="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-3xl p-2 shadow-lg">
              <RealtimeStats />
            </div>
          </div>

          <!-- 内容流组件 -->
          <div class="lg:col-span-2">
            <div class="bg-gradient-to-br from-gray-50 to-blue-50 rounded-3xl p-2 shadow-lg">
              <ContentFeed />
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 text-white py-16">
      <div class="container mx-auto px-6">
        <div class="max-w-4xl mx-auto text-center">
          <!-- 主要内容 -->
          <div class="mb-12">
            <h3 class="text-3xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-pink-500 bg-clip-text text-transparent">
              关注官方获取最新资讯
            </h3>
            <p class="text-gray-300 mb-8 text-lg">
              第一时间获取活动更新、奖励发放和精彩内容
            </p>

            <!-- 社交媒体按钮 -->
            <div class="flex justify-center space-x-6">
              <button class="group bg-green-500 hover:bg-green-600 text-white p-4 rounded-2xl transition-all duration-300 transform hover:scale-110 shadow-lg">
                <div class="flex flex-col items-center">
                  <span class="text-2xl mb-1">💬</span>
                  <span class="text-sm font-medium">微信</span>
                </div>
              </button>
              <button class="group bg-red-500 hover:bg-red-600 text-white p-4 rounded-2xl transition-all duration-300 transform hover:scale-110 shadow-lg">
                <div class="flex flex-col items-center">
                  <span class="text-2xl mb-1">📱</span>
                  <span class="text-sm font-medium">微博</span>
                </div>
              </button>
              <button class="group bg-black hover:bg-gray-800 text-white p-4 rounded-2xl transition-all duration-300 transform hover:scale-110 shadow-lg">
                <div class="flex flex-col items-center">
                  <span class="text-2xl mb-1">🎵</span>
                  <span class="text-sm font-medium">抖音</span>
                </div>
              </button>
            </div>
          </div>

          <!-- 分隔线 -->
          <div class="border-t border-white/20 pt-8">
            <div class="grid md:grid-cols-3 gap-6 text-center">
              <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-4">
                <div class="text-lg font-semibold text-yellow-400 mb-1">活动时间</div>
                <div class="text-gray-300">即日起至2025年7月20日</div>
              </div>
              <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-4">
                <div class="text-lg font-semibold text-pink-400 mb-1">主办方</div>
                <div class="text-gray-300">厦门文旅 × 厦门地铁</div>
              </div>
              <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-4">
                <div class="text-lg font-semibold text-blue-400 mb-1">合作平台</div>
                <div class="text-gray-300">小红书 × 抖音</div>
              </div>
            </div>

            <div class="mt-8 text-gray-400">
              <p class="text-lg">让我们一起在音乐的海洋中遇见最美的厦门 🎵</p>
              <p class="text-sm mt-2">© 2025 周同学CHOUCHOU厦门站活动官网</p>
            </div>
          </div>
        </div>
      </div>
    </footer>

    <!-- Point Detail Modal -->
    <div v-if="selectedPoint" class="modal modal-open">
      <div class="modal-box max-w-2xl">
        <h3 class="font-bold text-lg">{{ selectedPoint.name }}</h3>
        <img :src="selectedPoint.image" :alt="selectedPoint.name" class="w-full h-64 object-cover rounded-lg my-4">
        <p class="py-4">{{ selectedPoint.fullDescription }}</p>
        
        <div class="grid grid-cols-2 gap-4 my-4">
          <div class="bg-blue-50 p-3 rounded">
            <div class="font-semibold text-blue-800">最佳拍摄时间</div>
            <div class="text-sm">{{ selectedPoint.bestTime }}</div>
          </div>
          <div class="bg-green-50 p-3 rounded">
            <div class="font-semibold text-green-800">推荐装备</div>
            <div class="text-sm">{{ selectedPoint.equipment }}</div>
          </div>
        </div>
        
        <div class="modal-action">
          <button class="btn btn-primary">开始导航</button>
          <button class="btn" @click="selectedPoint = null">关闭</button>
        </div>
      </div>
    </div>

    <!-- Search Modal -->
    <div v-if="showSearchModal" class="modal modal-open">
      <div class="modal-box max-w-6xl max-h-[90vh] overflow-y-auto">
        <button
          @click="showSearchModal = false"
          class="btn btn-sm btn-circle absolute right-2 top-2 z-10"
        >
          ✕
        </button>
        
        <XiaoHongShuSearch />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import XiaoHongShuSearch from '../components/XiaoHongShuSearch.vue'
import RealtimeStats from '../components/RealtimeStats.vue'
import ContentFeed from '../components/ContentFeed.vue'
import realDataService from '../services/realDataService.js'

// Reactive data
const remainingDays = ref(calculateRemainingDays())
const totalCheckins = ref(18500)
const onlineUsers = ref(2800)
const selectedPoint = ref(null)
const activeRoute = ref(0)
const lastUpdate = ref(new Date().toLocaleTimeString())
const showSearchModal = ref(false)
const realtimeData = ref({})

// Music notes animation data
const musicNotes = ref([
  { id: 1, symbol: '♪', class: 'absolute text-4xl text-yellow-400 animate-pulse floating-note-1', left: '10%', delay: '0s' },
  { id: 2, symbol: '♫', class: 'absolute text-3xl text-pink-400 animate-bounce floating-note-2', left: '80%', delay: '1s' },
  { id: 3, symbol: '♬', class: 'absolute text-5xl text-blue-400 animate-ping floating-note-3', left: '60%', delay: '2s' },
  { id: 4, symbol: '♩', class: 'absolute text-2xl text-green-400 animate-pulse floating-note-4', left: '30%', delay: '3s' },
])

// Check-in points data
const checkinPoints = ref([
  {
    name: '十里长堤5.5米大娃',
    description: '草坪音乐会与巨型雕塑的完美结合',
    image: 'https://images.unsplash.com/photo-1514924013411-cbf25faa35bb?w=400',
    badge: '人气王',
    subway: '地铁1号线',
    rating: 5,
    tips: '傍晚最佳',
    bestTime: '傍晚6-7点，利用落日逆光拍摄',
    equipment: '广角镜头、浅色长裙',
    fullDescription: '5.5米高的周同学巨型雕塑矗立在十里长堤，配合草坪音乐会营造浪漫氛围。傍晚时分，落日余晖洒向雕塑，是拍摄的黄金时间。'
  },
  {
    name: '海上世界音乐台阶',
    description: 'AR互动与《晴天》旋律的奇妙体验',
    image: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400',
    badge: '科技感',
    subway: '地铁2号线',
    rating: 5,
    tips: '夜间最美',
    bestTime: '夜间开启慢动作拍摄踩踏效果',
    equipment: '手机慢动作模式、三脚架',
    fullDescription: '踩踏台阶触发《晴天》旋律，夜晚灯光效果更佳。通过抖音扫描雕塑可生成周同学弹奏钢琴的AR特效。'
  },
  {
    name: 'SM城市广场7米雕塑',
    description: '购物天堂里的音乐圣地',
    image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400',
    badge: '便利之选',
    subway: '地铁1号线',
    rating: 4,
    tips: '人流较少',
    bestTime: '上午10点或下午3点人流较少',
    equipment: '仰角拍摄突出雕塑高度',
    fullDescription: '7米高雕塑位于SM城市广场，周边配套完善。消费满199元可抽扭蛋机，100%中奖获得签名海报或演唱会抵扣券。'
  },
  {
    name: '中山路骑楼街区',
    description: '闽南文化与现代音乐的碰撞',
    image: 'https://images.unsplash.com/photo-1545565597-6c0fefe2c9c4?w=400',
    badge: '文化底蕴',
    subway: '地铁1号线',
    rating: 4,
    tips: '文创丰富',
    bestTime: '全天候，建议避开用餐高峰',
    equipment: '复古滤镜、闽南元素道具',
    fullDescription: '骑楼建筑承载百年历史，《爱在鹭岛 诗笺漫游》明信片套装可用紫外线灯解锁隐藏歌词，是独特的文创体验。'
  },
  {
    name: '鼓浪屿IP元素',
    description: '钢琴之岛的音乐传承',
    image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400',
    badge: '经典',
    subway: '轮渡+步行',
    rating: 5,
    tips: '需要门票',
    bestTime: '上午购买联票避开人流高峰',
    equipment: '长焦镜头、防晒用品',
    fullDescription: '菽庄花园钢琴码头与周同学元素完美融合，50元联票含五大景点，在龙头路邮政支局可加盖专属纪念印章。'
  },
  {
    name: '荻花洲艺术装置',
    description: '现代艺术与音乐美学的结合',
    image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400',
    badge: '艺术感',
    subway: '地铁3号线',
    rating: 4,
    tips: '避开拥堵',
    bestTime: '白天镜面反射，傍晚剪影效果',
    equipment: '长焦镜头、偏振镜',
    fullDescription: '利用镜面反射效果拍摄对称构图，傍晚时分可捕捉雕塑与落日的美丽剪影，地铁3号线直达避免热门点位排队。'
  }
])

// Points system data
const pointTasks = ref([
  { name: '每日签到', description: '连续签到获得额外奖励', icon: '📅', points: 50, limit: '每日1次' },
  { name: '扫码乘车', description: '使用厦门地铁小程序乘车', icon: '🚇', points: 100, limit: '每日不限' },
  { name: '邀请好友', description: '好友完成注册即可获得', icon: '👥', points: 100, limit: '上限500豆/日' },
  { name: '打卡点扫码', description: '六大打卡点扫描二维码', icon: '📍', points: 200, limit: '每日2次' },
  { name: 'NPC互动', description: '快闪店内与NPC对话', icon: '🤖', points: '50-100', limit: '7月11-13日' }
])

const rewards = ref([
  { name: '星际光轮篮球', description: '限量版周同学主题篮球', icon: '🏀', cost: '2000豆', stock: '每日5个' },
  { name: '9.9元唱游卡', description: '8次乘车+商圈餐饮券', icon: '🎫', cost: '优惠价', stock: '充足' },
  { name: '快闪店满减券', description: '满99元减20元优惠券', icon: '🎟️', cost: '300豆', stock: '充足' },
  { name: 'P+R停车券', description: 'Park+Ride停车抵扣', icon: '🅿️', cost: '积分兑换', stock: '有限' }
])

// Route planning data
const routes = ref([
  {
    name: '经典一日游',
    steps: [
      { time: '09:00', location: 'SM城市广场', description: '7米雕塑打卡+快闪店购物', tips: '消费满199元抽扭蛋机' },
      { time: '11:00', location: '中山路骑楼', description: '文创购买+闽南文化体验', tips: '购买明信片套装纪念' },
      { time: '14:00', location: '海上世界', description: '音乐台阶+AR互动体验', tips: '夜晚效果更佳，可晚点再来' },
      { time: '16:00', location: '鼓浪屿', description: '钢琴码头+五大景点游览', tips: '购买50元联票性价比高' },
      { time: '18:00', location: '十里长堤', description: '草坪音乐会+落日拍摄', tips: '傍晚光线最佳，准备广角镜头' },
      { time: '20:00', location: '荻花洲', description: '夜景拍摄+艺术装置', tips: '地铁3号线直达避免拥堵' }
    ]
  },
  {
    name: '快闪购物游',
    steps: [
      { time: '10:00', location: 'SM城市广场快闪店', description: '抢购限量篮球+扭蛋体验', tips: '开门即到，避免售罄' },
      { time: '12:00', location: '海上世界快闪店', description: '潮汐之眼冰箱贴+AR拍摄', tips: '15:00有补货，可等待' },
      { time: '15:00', location: '中山路快闪店', description: '文创套装+邮政印章', tips: '龙头路邮政支局加盖印章' },
      { time: '17:00', location: '各打卡点', description: '完成集章任务', tips: '6枚印章+消费满199元获徽章' }
    ]
  },
  {
    name: '积分刷豆游',
    steps: [
      { time: '全天', location: '厦门地铁', description: '扫码乘车+邀请好友', tips: '每次乘车100豆，邀请奖励丰厚' },
      { time: '定点', location: '打卡点', description: '每日2次扫码任务', tips: '每次200豆，选择人少的点位' },
      { time: '7.11-7.13', location: '快闪店', description: 'NPC互动获得额外积分', tips: '限时活动，不要错过' }
    ]
  }
])

// Real-time data
const recentWinners = ref([
  { id: 1, name: '用户***1', prize: '篮球' },
  { id: 2, name: '用户***2', prize: '海报' },
  { id: 3, name: '用户***3', prize: '冰箱贴' },
  { id: 4, name: '用户***4', prize: '徽章' }
])

const todaySongs = ref([
  { time: '10:00', name: '晴天' },
  { time: '14:00', name: '稻香' },
  { time: '16:00', name: '青花瓷' },
  { time: '19:00', name: '七里香' }
])

// Methods
function calculateRemainingDays() {
  const endDate = new Date('2025-07-20')
  const today = new Date()
  const diffTime = endDate - today
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
}

function scrollToSection(sectionId) {
  document.getElementById(sectionId)?.scrollIntoView({ behavior: 'smooth' })
}

function showPointDetail(point) {
  selectedPoint.value = point
}

function formatNumber(num) {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toLocaleString()
}

async function updateRealtimeData() {
  try {
    const data = await realDataService.getRealtimeData()
    totalCheckins.value = data.totalCheckins
    onlineUsers.value = data.onlineUsers
    realtimeData.value = data
  } catch (error) {
    console.error('更新实时数据失败:', error)
  }
}



// Update data periodically
onMounted(async () => {
  // 初始加载实时数据
  await updateRealtimeData()
  
  // 每30秒更新一次数据
  setInterval(async () => {
    await updateRealtimeData()
    lastUpdate.value = new Date().toLocaleTimeString()
  }, 30000)
  
  // 每5秒小幅更新计数器
  setInterval(() => {
    totalCheckins.value += Math.floor(Math.random() * 3) + 1
    onlineUsers.value += Math.floor(Math.random() * 20) - 10
    if (onlineUsers.value < 2000) onlineUsers.value = 2000 + Math.floor(Math.random() * 1000)
  }, 5000)
})
</script>

<style scoped>
.floating-note-1 {
  animation: float1 4s ease-in-out infinite;
}

.floating-note-2 {
  animation: float2 5s ease-in-out infinite;
}

.floating-note-3 {
  animation: float3 3s ease-in-out infinite;
}

.floating-note-4 {
  animation: float4 6s ease-in-out infinite;
}

@keyframes float1 {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes float2 {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-30px) rotate(-180deg); }
}

@keyframes float3 {
  0%, 100% { transform: translateY(0px) scale(1); }
  50% { transform: translateY(-25px) scale(1.2); }
}

@keyframes float4 {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-15px) rotate(120deg); }
  66% { transform: translateY(-10px) rotate(240deg); }
}

.timeline-vertical .timeline-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 50%;
  top: 3rem;
  height: calc(100% - 3rem);
  width: 2px;
  background-color: #e5e7eb;
  transform: translateX(-50%);
}

.timeline-marker {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  z-index: 10;
  position: relative;
}
</style> 