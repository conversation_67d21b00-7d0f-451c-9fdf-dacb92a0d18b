<template>
  <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
    <!-- 头部控制区 -->
    <div class="bg-gradient-to-r from-pink-500 to-purple-600 text-white p-6">
      <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h3 class="text-2xl font-bold mb-2">🔥 实时内容流</h3>
          <p class="text-pink-100">来自小红书的真实用户分享</p>
        </div>
        
        <div class="flex flex-wrap gap-2">
          <select v-model="selectedKeyword" @change="loadContent" 
                  class="px-3 py-1 rounded-lg text-gray-800 text-sm">
            <option value="周杰伦">周杰伦专列</option>
            <option value="厦门">厦门旅游</option>
            <option value="地铁">地铁出行</option>
          </select>
          
          <select v-model="sortType" @change="loadContent"
                  class="px-3 py-1 rounded-lg text-gray-800 text-sm">
            <option value="general">综合排序</option>
            <option value="hot_desc">热度排序</option>
            <option value="create_time_desc">最新发布</option>
            <option value="like_desc">点赞最多</option>
          </select>
          
          <button @click="toggleRealData" 
                  :class="['px-3 py-1 rounded-lg text-sm font-medium transition-all', 
                          useRealData ? 'bg-green-400 text-green-900' : 'bg-white/20 text-white']">
            {{ useRealData ? '实时数据' : '模拟数据' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="p-8 text-center">
      <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
      <p class="mt-2 text-gray-600">加载中...</p>
    </div>

    <!-- 内容列表 -->
    <div v-else class="max-h-96 overflow-y-auto">
      <div v-for="(note, index) in contentData.notes" :key="note.id" 
           class="border-b border-gray-100 hover:bg-gray-50 transition-colors">
        <div class="p-4">
          <!-- 用户信息 -->
          <div class="flex items-center mb-3">
            <img :src="note.author.avatar" :alt="note.author.name" 
                 class="w-10 h-10 rounded-full object-cover">
            <div class="ml-3 flex-1">
              <div class="flex items-center">
                <span class="font-semibold text-gray-800">{{ note.author.name }}</span>
                <span v-if="note.author.is_verified" class="ml-1 text-blue-500">✅</span>
                <span class="ml-2 text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                  {{ note.author.level }}
                </span>
              </div>
              <div class="text-sm text-gray-500">
                {{ formatNumber(note.author.follower_count) }} 粉丝
                <span v-if="note.location" class="ml-2">📍 {{ note.location }}</span>
              </div>
            </div>
            <div class="text-xs text-gray-400">
              {{ formatTime(note.publish_time) }}
            </div>
          </div>

          <!-- 内容 -->
          <div class="mb-3">
            <h4 class="font-semibold text-gray-800 mb-2 line-clamp-2">{{ note.title }}</h4>
            <p class="text-gray-600 text-sm line-clamp-3">{{ note.content }}</p>
          </div>

          <!-- 图片网格 -->
          <div v-if="note.images && note.images.length > 0" class="mb-3">
            <div :class="getImageGridClass(note.images.length)">
              <img v-for="(image, imgIndex) in note.images.slice(0, 6)" 
                   :key="imgIndex"
                   :src="image" 
                   :alt="`图片${imgIndex + 1}`"
                   class="w-full h-full object-cover rounded-lg cursor-pointer hover:scale-105 transition-transform"
                   @click="openImageModal(note.images, imgIndex)"
                   loading="lazy">
              
              <!-- 更多图片提示 -->
              <div v-if="note.images.length > 6" 
                   class="relative bg-gray-200 rounded-lg flex items-center justify-center cursor-pointer hover:bg-gray-300 transition-colors"
                   @click="openImageModal(note.images, 6)">
                <span class="text-gray-600 font-semibold">+{{ note.images.length - 6 }}</span>
              </div>
            </div>
          </div>

          <!-- 标签 -->
          <div class="mb-3">
            <span v-for="tag in note.tags" :key="tag"
                  class="inline-block bg-blue-50 text-blue-600 text-xs px-2 py-1 rounded mr-2 mb-1">
              #{{ tag }}
            </span>
          </div>

          <!-- 互动数据 -->
          <div class="flex items-center justify-between text-sm text-gray-500">
            <div class="flex items-center space-x-4">
              <span class="flex items-center">
                <span class="text-red-500 mr-1">❤️</span>
                {{ formatNumber(note.stats.likes) }}
              </span>
              <span class="flex items-center">
                <span class="text-blue-500 mr-1">💬</span>
                {{ formatNumber(note.stats.comments) }}
              </span>
              <span class="flex items-center">
                <span class="text-green-500 mr-1">🔄</span>
                {{ formatNumber(note.stats.shares) }}
              </span>
              <span class="flex items-center">
                <span class="text-yellow-500 mr-1">⭐</span>
                {{ formatNumber(note.stats.collections) }}
              </span>
            </div>
            
            <div class="flex items-center space-x-2">
              <span v-if="note.engagement_rate" class="text-xs bg-green-100 text-green-600 px-2 py-1 rounded">
                互动率 {{ note.engagement_rate }}%
              </span>
              <span :class="['text-xs px-2 py-1 rounded', 
                            note.source === 'real_api' ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-600']">
                {{ note.source === 'real_api' ? '实时' : '精选' }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载更多 -->
      <div class="p-4 text-center border-t">
        <button @click="loadMore" 
                :disabled="loadingMore"
                class="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
          {{ loadingMore ? '加载中...' : '加载更多' }}
        </button>
      </div>
    </div>

    <!-- 数据洞察 -->
    <div v-if="contentData.notes && contentData.notes.length > 0" 
         class="bg-gray-50 p-4 border-t">
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
        <div>
          <div class="text-lg font-bold text-purple-600">{{ contentData.total || 0 }}</div>
          <div class="text-xs text-gray-500">总内容数</div>
        </div>
        <div>
          <div class="text-lg font-bold text-pink-600">{{ averageEngagement }}%</div>
          <div class="text-xs text-gray-500">平均互动率</div>
        </div>
        <div>
          <div class="text-lg font-bold text-blue-600">{{ totalLikes }}</div>
          <div class="text-xs text-gray-500">总点赞数</div>
        </div>
        <div>
          <div class="text-lg font-bold text-green-600">{{ contentData.data_source || '数据源' }}</div>
          <div class="text-xs text-gray-500">数据来源</div>
        </div>
      </div>
    </div>

    <!-- 图片查看模态框 -->
    <div v-if="showImageModal" 
         class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50"
         @click="closeImageModal">
      <div class="max-w-4xl max-h-screen p-4">
        <img :src="modalImages[currentImageIndex]" 
             class="max-w-full max-h-full object-contain"
             @click.stop>
        <div class="flex justify-center mt-4 space-x-2">
          <button v-for="(_, index) in modalImages" :key="index"
                  @click="currentImageIndex = index"
                  :class="['w-3 h-3 rounded-full transition-colors',
                          index === currentImageIndex ? 'bg-white' : 'bg-white/50']">
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import realDataService from '../services/realDataService.js'

// 响应式数据
const loading = ref(false)
const loadingMore = ref(false)
const selectedKeyword = ref('周杰伦')
const sortType = ref('general')
const useRealData = ref(false)
const currentPage = ref(1)

const contentData = ref({
  notes: [],
  total: 0,
  keyword: '',
  data_source: ''
})

// 图片模态框
const showImageModal = ref(false)
const modalImages = ref([])
const currentImageIndex = ref(0)

// 计算属性
const averageEngagement = computed(() => {
  if (!contentData.value.notes || contentData.value.notes.length === 0) return 0
  const total = contentData.value.notes.reduce((sum, note) => sum + (parseFloat(note.engagement_rate) || 0), 0)
  return (total / contentData.value.notes.length).toFixed(1)
})

const totalLikes = computed(() => {
  if (!contentData.value.notes || contentData.value.notes.length === 0) return 0
  const total = contentData.value.notes.reduce((sum, note) => sum + note.stats.likes, 0)
  return formatNumber(total)
})

// 方法
function formatNumber(num) {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

function formatTime(timeString) {
  if (!timeString) return ''
  const date = new Date(timeString)
  const now = new Date()
  const diffInHours = (now - date) / (1000 * 60 * 60)
  
  if (diffInHours < 1) {
    return Math.floor(diffInHours * 60) + '分钟前'
  } else if (diffInHours < 24) {
    return Math.floor(diffInHours) + '小时前'
  } else {
    return Math.floor(diffInHours / 24) + '天前'
  }
}

function getImageGridClass(count) {
  if (count === 1) return 'grid grid-cols-1 gap-2 max-w-sm'
  if (count === 2) return 'grid grid-cols-2 gap-2 h-32'
  if (count === 3) return 'grid grid-cols-3 gap-2 h-24'
  if (count === 4) return 'grid grid-cols-2 gap-2 h-32'
  return 'grid grid-cols-3 gap-2 h-32'
}

function openImageModal(images, startIndex) {
  modalImages.value = images
  currentImageIndex.value = startIndex
  showImageModal.value = true
}

function closeImageModal() {
  showImageModal.value = false
  modalImages.value = []
  currentImageIndex.value = 0
}

function toggleRealData() {
  useRealData.value = !useRealData.value
  loadContent()
}

async function loadContent() {
  loading.value = true
  currentPage.value = 1
  
  try {
    const result = await realDataService.callMCP('search_xiaohongshu_notes', {
      keyword: selectedKeyword.value,
      page: currentPage.value,
      sort_type: sortType.value,
      use_real_data: useRealData.value
    })
    
    contentData.value = result
  } catch (error) {
    console.error('加载内容失败:', error)
    // 使用降级数据
    contentData.value = {
      notes: [],
      total: 0,
      keyword: selectedKeyword.value,
      data_source: '降级模式'
    }
  } finally {
    loading.value = false
  }
}

async function loadMore() {
  if (loadingMore.value) return
  
  loadingMore.value = true
  currentPage.value += 1
  
  try {
    const result = await realDataService.callMCP('search_xiaohongshu_notes', {
      keyword: selectedKeyword.value,
      page: currentPage.value,
      sort_type: sortType.value,
      use_real_data: useRealData.value
    })
    
    // 合并数据
    contentData.value.notes = [...contentData.value.notes, ...result.notes]
  } catch (error) {
    console.error('加载更多失败:', error)
    currentPage.value -= 1
  } finally {
    loadingMore.value = false
  }
}

// 生命周期
onMounted(() => {
  loadContent()
})
</script>

<style scoped>
/* 文本截断 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style> 