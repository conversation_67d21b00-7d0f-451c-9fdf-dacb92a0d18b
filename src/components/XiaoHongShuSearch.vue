<template>
  <div class="xiaohongshu-search">
    <!-- Search Header -->
    <div class="bg-gradient-to-r from-red-500 to-pink-500 text-white p-6 rounded-t-lg">
      <h2 class="text-2xl font-bold flex items-center">
        <span class="text-3xl mr-3">📱</span>
        小红书内容搜索
      </h2>
      <p class="text-sm opacity-90 mt-2">发现更多周同学厦门活动的精彩内容</p>
    </div>

    <!-- Search Form -->
    <div class="bg-white p-6 border-b">
      <div class="flex gap-4 mb-4">
        <div class="flex-1">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索关键词..."
            class="input input-bordered w-full"
            @keyup.enter="performSearch"
          />
        </div>
        <select v-model="searchType" class="select select-bordered">
          <option value="notes">笔记</option>
          <option value="users">用户</option>
        </select>
        <button
          @click="performSearch"
          :disabled="isSearching || !searchQuery.trim()"
          class="btn btn-primary"
        >
          <span v-if="isSearching" class="loading loading-spinner loading-sm"></span>
          {{ isSearching ? '搜索中...' : '搜索' }}
        </button>
      </div>
      
      <!-- Quick Search Tags -->
      <div class="flex flex-wrap gap-2">
        <span class="text-sm text-gray-600 mr-2">热门标签:</span>
        <button
          v-for="tag in quickTags"
          :key="tag"
          @click="quickSearch(tag)"
          class="badge badge-outline hover:badge-primary cursor-pointer"
        >
          {{ tag }}
        </button>
        <button
          @click="testDataService"
          class="badge badge-info cursor-pointer ml-2"
          title="测试数据服务"
        >
          🧪 测试
        </button>
      </div>
    </div>

    <!-- Search Results -->
    <div class="bg-gray-50 p-6 rounded-b-lg">
      <div v-if="isSearching" class="text-center py-8">
        <div class="loading loading-spinner loading-lg"></div>
        <p class="mt-4 text-gray-600">正在搜索相关内容...</p>
      </div>

      <div v-else-if="searchResults.length > 0" class="space-y-4">
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-semibold">
            搜索结果 ({{ searchResults.length }} 条)
          </h3>
          <div class="flex gap-2">
            <select v-model="sortType" @change="sortResults" class="select select-sm">
              <option value="hot_desc">热度最高</option>
              <option value="time_desc">最新发布</option>
              <option value="like_desc">点赞最多</option>
            </select>
          </div>
        </div>

        <!-- Results Grid -->
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div
            v-for="(item, index) in paginatedResults"
            :key="index"
            class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer"
            @click="openDetail(item)"
          >
            <div class="p-4">
              <div class="flex justify-between items-start mb-2">
                <h4 class="font-medium text-gray-800 line-clamp-2">{{ item.title }}</h4>
                <div class="flex items-center text-xs text-gray-500">
                  <span class="mr-1">🔥</span>
                  <span>{{ getItemLikes(item) }}</span>
                </div>
              </div>

              <p class="text-sm text-gray-600 mb-3 line-clamp-2">{{ getItemDescription(item) }}</p>

              <div class="flex justify-between items-center text-xs text-gray-500">
                <span>@{{ getItemAuthor(item) }}</span>
                <span>{{ formatItemTime(item) }}</span>
              </div>

              <div class="flex flex-wrap gap-1 mt-2">
                <span
                  v-for="tag in getItemTags(item).slice(0, 3)"
                  :key="tag"
                  class="badge badge-sm badge-outline"
                >
                  {{ tag }}
                </span>
              </div>

              <div class="flex items-center justify-between mt-3 pt-3 border-t">
                <div class="flex items-center space-x-4 text-xs">
                  <span class="flex items-center">
                    <span class="text-red-500 mr-1">❤️</span>
                    {{ getItemLikes(item) }}
                  </span>
                  <span class="flex items-center">
                    <span class="text-blue-500 mr-1">💬</span>
                    {{ getItemComments(item) }}
                  </span>
                  <span class="flex items-center">
                    <span class="text-green-500 mr-1">🔄</span>
                    {{ getItemShares(item) }}
                  </span>
                </div>
                <a
                  :href="getItemLink(item)"
                  target="_blank"
                  class="text-blue-500 hover:text-blue-700 text-xs"
                  @click.stop
                >
                  查看原文 →
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- Pagination -->
        <div v-if="totalPages > 1" class="flex justify-center mt-6">
          <div class="join">
            <button
              v-for="page in totalPages"
              :key="page"
              @click="currentPage = page"
              :class="[
                'join-item btn btn-sm',
                { 'btn-active': currentPage === page }
              ]"
            >
              {{ page }}
            </button>
          </div>
        </div>
      </div>

      <div v-else-if="hasSearched && !isSearching" class="text-center py-8">
        <div class="text-6xl mb-4">🔍</div>
        <p class="text-gray-600">没有找到相关内容</p>
        <p class="text-sm text-gray-500 mt-2">试试其他关键词吧</p>
      </div>

      <div v-else class="text-center py-8">
        <div class="text-6xl mb-4">🎵</div>
        <p class="text-gray-600">输入关键词开始搜索</p>
        <p class="text-sm text-gray-500 mt-2">发现更多周同学相关内容</p>
      </div>
    </div>

    <!-- Detail Modal -->
    <div v-if="selectedItem" class="modal modal-open">
      <div class="modal-box max-w-2xl">
        <button
          @click="selectedItem = null"
          class="btn btn-sm btn-circle absolute right-2 top-2"
        >
          ✕
        </button>
        
        <h3 class="font-bold text-lg mb-4">{{ selectedItem.title }}</h3>
        
        <div class="space-y-4">
          <div class="flex items-center space-x-3">
            <div class="avatar">
              <div class="w-10 h-10 rounded-full">
                <img
                  :src="getItemAvatar(selectedItem)"
                  :alt="getItemAuthor(selectedItem)"
                  class="w-full h-full object-cover"
                  @error="handleAvatarError"
                />
              </div>
            </div>
            <div>
              <div class="font-medium">{{ getItemAuthor(selectedItem) }}</div>
              <div class="text-sm text-gray-500">{{ formatItemTime(selectedItem) }}</div>
            </div>
          </div>

          <div class="bg-gray-50 p-4 rounded-lg">
            <p class="text-gray-700">{{ getItemDescription(selectedItem) }}</p>
          </div>

          <!-- 图片展示 -->
          <div v-if="getItemImages(selectedItem).length > 0" class="grid grid-cols-3 gap-2">
            <img
              v-for="(image, idx) in getItemImages(selectedItem).slice(0, 6)"
              :key="idx"
              :src="image.url"
              :alt="`图片 ${idx + 1}`"
              class="w-full h-20 object-cover rounded cursor-pointer hover:opacity-80"
              @error="handleImageError"
            />
          </div>

          <div class="flex flex-wrap gap-2">
            <span
              v-for="tag in getItemTags(selectedItem)"
              :key="tag"
              class="badge badge-primary badge-outline"
            >
              {{ tag }}
            </span>
          </div>
          
          <div class="flex justify-between items-center pt-4 border-t">
            <div class="flex items-center space-x-6">
              <span class="flex items-center text-red-500">
                <span class="mr-1">❤️</span>
                {{ getItemLikes(selectedItem) }}
              </span>
              <span class="flex items-center text-blue-500">
                <span class="mr-1">💬</span>
                {{ getItemComments(selectedItem) }}
              </span>
              <span class="flex items-center text-green-500">
                <span class="mr-1">🔄</span>
                {{ getItemShares(selectedItem) }}
              </span>
            </div>
            <a
              :href="getItemLink(selectedItem)"
              target="_blank"
              class="btn btn-primary btn-sm"
            >
              查看原文
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { searchXiaoHongShuNotes, searchXiaoHongShuUsers, getHotTopics } from '../services/mcpSearchService.js'

const searchQuery = ref('')
const searchType = ref('notes')
const isSearching = ref(false)
const searchResults = ref([])
const selectedItem = ref(null)
const hasSearched = ref(false)
const sortType = ref('hot_desc')
const currentPage = ref(1)
const pageSize = 6

const quickTags = ref(getHotTopics().slice(0, 9))

const paginatedResults = computed(() => {
  const start = (currentPage.value - 1) * pageSize
  const end = start + pageSize
  return searchResults.value.slice(start, end)
})

const totalPages = computed(() => {
  return Math.ceil(searchResults.value.length / pageSize)
})

const performSearch = async () => {
  if (!searchQuery.value.trim()) return
  
  isSearching.value = true
  hasSearched.value = true
  
  try {
    // 调用我们的小红书搜索MCP工具
    const response = await callMCPSearch(searchQuery.value, searchType.value)
    searchResults.value = response
    currentPage.value = 1
  } catch (error) {
    console.error('搜索失败:', error)
    // 使用模拟数据作为fallback
    searchResults.value = generateMockResults(searchQuery.value)
  } finally {
    isSearching.value = false
  }
}

const callMCPSearch = async (keyword, type) => {
  try {
    if (type === 'notes') {
      return await searchXiaoHongShuNotes(keyword, {
        page: 1,
        sort_type: sortType.value
      })
    } else {
      return await searchXiaoHongShuUsers(keyword, {
        page: 1,
        sort_type: sortType.value
      })
    }
  } catch (error) {
    console.error('MCP搜索失败:', error)
    // 降级到模拟数据
    return generateMockResults(keyword)
  }
}

const generateMockResults = (keyword) => {
  const mockResults = []
  for (let i = 1; i <= 15; i++) {
    mockResults.push({
      title: `关于${keyword}的精彩分享 #${i}`,
      author: `博主${i}`,
      description: `这是一篇关于${keyword}的详细分享，包含实用技巧和心得体验。涵盖了最新的活动信息、打卡攻略、以及个人的真实感受和建议。`,
      likes: Math.floor(Math.random() * 500) + 50,
      comments: Math.floor(Math.random() * 100) + 10,
      shares: Math.floor(Math.random() * 50) + 5,
      tags: [keyword, '分享', '推荐', '攻略', '体验'].slice(0, Math.floor(Math.random() * 3) + 2),
      time: `2024-12-${String(Math.floor(Math.random() * 30) + 1).padStart(2, '0')}`,
      link: `https://www.xiaohongshu.com/explore/note_${i}`
    })
  }
  return mockResults
}

const quickSearch = (tag) => {
  searchQuery.value = tag
  performSearch()
}

const sortResults = () => {
  switch (sortType.value) {
    case 'hot_desc':
      searchResults.value.sort((a, b) => getItemLikes(b) - getItemLikes(a))
      break
    case 'time_desc':
      searchResults.value.sort((a, b) => {
        const timeA = typeof a.time === 'number' ? a.time : new Date(a.time).getTime()
        const timeB = typeof b.time === 'number' ? b.time : new Date(b.time).getTime()
        return timeB - timeA
      })
      break
    case 'like_desc':
      searchResults.value.sort((a, b) => getItemLikes(b) - getItemLikes(a))
      break
  }
}

const openDetail = (item) => {
  selectedItem.value = item
}

// 数据访问辅助函数 - 兼容新旧数据格式
const getItemLikes = (item) => {
  return item.interact_info?.liked_count || item.likes || 0
}

const getItemComments = (item) => {
  return item.interact_info?.comment_count || item.comments || 0
}

const getItemShares = (item) => {
  return item.interact_info?.share_count || item.shares || 0
}

const getItemAuthor = (item) => {
  return item.user?.nickname || item.author || '未知用户'
}

const getItemDescription = (item) => {
  return item.desc || item.description || ''
}

const getItemTags = (item) => {
  if (item.tag_list) {
    return item.tag_list.map(tag => typeof tag === 'object' ? tag.name : tag)
  }
  return item.tags || []
}

const getItemLink = (item) => {
  return item.note_url || item.link || '#'
}

const getItemAvatar = (item) => {
  return item.user?.avatar || `https://picsum.photos/100/100?random=${item.id}`
}

const getItemImages = (item) => {
  return item.image_list || []
}

const formatItemTime = (item) => {
  if (item.time && typeof item.time === 'number') {
    // 时间戳格式
    const now = Date.now()
    const diff = now - item.time
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (days > 0) return `${days}天前`
    if (hours > 0) return `${hours}小时前`
    if (minutes > 0) return `${minutes}分钟前`
    return '刚刚'
  }
  return item.time || '未知时间'
}

const handleAvatarError = (event) => {
  event.target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent('用户')}&background=random`
}

const handleImageError = (event) => {
  event.target.src = 'https://via.placeholder.com/400x600/f0f0f0/999999?text=图片加载失败'
}

const testDataService = async () => {
  console.log('🧪 手动测试数据服务...')
  try {
    const result = await searchXiaoHongShuNotes('周杰伦', { limit: 3 })
    console.log('✅ 测试成功，获取到数据:', result)
    alert(`测试成功！获取到 ${result.length} 条数据，请查看控制台了解详情。`)
  } catch (error) {
    console.error('❌ 测试失败:', error)
    alert(`测试失败: ${error.message}`)
  }
}

onMounted(async () => {
  // 组件挂载时可以执行一些初始化操作
  console.log('🎵 小红书搜索组件已加载')

  // 自动测试数据服务
  try {
    const testResult = await testRealDataConnection()
    if (testResult.success) {
      console.log('✅ 数据服务测试成功:', testResult.message)
    } else {
      console.warn('⚠️ 数据服务测试失败:', testResult.message)
    }
  } catch (error) {
    console.error('❌ 数据服务测试出错:', error)
  }
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.xiaohongshu-search {
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}
</style> 