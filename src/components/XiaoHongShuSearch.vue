<template>
  <div class="xiaohongshu-search">
    <!-- Search Header -->
    <div class="bg-gradient-to-r from-red-500 to-pink-500 text-white p-6 rounded-t-lg">
      <h2 class="text-2xl font-bold flex items-center">
        <span class="text-3xl mr-3">📱</span>
        小红书内容搜索
      </h2>
      <p class="text-sm opacity-90 mt-2">发现更多周同学厦门活动的精彩内容</p>
    </div>

    <!-- Search Form -->
    <div class="bg-white p-6 border-b">
      <div class="flex gap-4 mb-4">
        <div class="flex-1">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索关键词..."
            class="input input-bordered w-full"
            @keyup.enter="performSearch"
          />
        </div>
        <select v-model="searchType" class="select select-bordered">
          <option value="notes">笔记</option>
          <option value="users">用户</option>
        </select>
        <button
          @click="performSearch"
          :disabled="isSearching || !searchQuery.trim()"
          class="btn btn-primary"
        >
          <span v-if="isSearching" class="loading loading-spinner loading-sm"></span>
          {{ isSearching ? '搜索中...' : '搜索' }}
        </button>
      </div>
      
      <!-- Quick Search Tags -->
      <div class="flex flex-wrap gap-2">
        <span class="text-sm text-gray-600 mr-2">热门标签:</span>
        <button
          v-for="tag in quickTags"
          :key="tag"
          @click="quickSearch(tag)"
          class="badge badge-outline hover:badge-primary cursor-pointer"
        >
          {{ tag }}
        </button>
      </div>
    </div>

    <!-- Search Results -->
    <div class="bg-gray-50 p-6 rounded-b-lg">
      <div v-if="isSearching" class="text-center py-8">
        <div class="loading loading-spinner loading-lg"></div>
        <p class="mt-4 text-gray-600">正在搜索相关内容...</p>
      </div>

      <div v-else-if="searchResults.length > 0" class="space-y-4">
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-semibold">
            搜索结果 ({{ searchResults.length }} 条)
          </h3>
          <div class="flex gap-2">
            <select v-model="sortType" @change="sortResults" class="select select-sm">
              <option value="hot_desc">热度最高</option>
              <option value="time_desc">最新发布</option>
              <option value="like_desc">点赞最多</option>
            </select>
          </div>
        </div>

        <!-- Results Grid -->
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div
            v-for="(item, index) in paginatedResults"
            :key="index"
            class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer"
            @click="openDetail(item)"
          >
            <div class="p-4">
              <div class="flex justify-between items-start mb-2">
                <h4 class="font-medium text-gray-800 line-clamp-2">{{ item.title }}</h4>
                <div class="flex items-center text-xs text-gray-500">
                  <span class="mr-1">🔥</span>
                  <span>{{ item.likes }}</span>
                </div>
              </div>
              
              <p class="text-sm text-gray-600 mb-3 line-clamp-2">{{ item.description }}</p>
              
              <div class="flex justify-between items-center text-xs text-gray-500">
                <span>@{{ item.author }}</span>
                <span>{{ item.time }}</span>
              </div>
              
              <div class="flex flex-wrap gap-1 mt-2">
                <span
                  v-for="tag in item.tags.slice(0, 3)"
                  :key="tag"
                  class="badge badge-sm badge-outline"
                >
                  {{ tag }}
                </span>
              </div>
              
              <div class="flex items-center justify-between mt-3 pt-3 border-t">
                <div class="flex items-center space-x-4 text-xs">
                  <span class="flex items-center">
                    <span class="text-red-500 mr-1">❤️</span>
                    {{ item.likes }}
                  </span>
                  <span class="flex items-center">
                    <span class="text-blue-500 mr-1">💬</span>
                    {{ item.comments }}
                  </span>
                  <span class="flex items-center">
                    <span class="text-green-500 mr-1">🔄</span>
                    {{ item.shares }}
                  </span>
                </div>
                <a 
                  :href="item.link" 
                  target="_blank"
                  class="text-blue-500 hover:text-blue-700 text-xs"
                  @click.stop
                >
                  查看原文 →
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- Pagination -->
        <div v-if="totalPages > 1" class="flex justify-center mt-6">
          <div class="join">
            <button
              v-for="page in totalPages"
              :key="page"
              @click="currentPage = page"
              :class="[
                'join-item btn btn-sm',
                { 'btn-active': currentPage === page }
              ]"
            >
              {{ page }}
            </button>
          </div>
        </div>
      </div>

      <div v-else-if="hasSearched && !isSearching" class="text-center py-8">
        <div class="text-6xl mb-4">🔍</div>
        <p class="text-gray-600">没有找到相关内容</p>
        <p class="text-sm text-gray-500 mt-2">试试其他关键词吧</p>
      </div>

      <div v-else class="text-center py-8">
        <div class="text-6xl mb-4">🎵</div>
        <p class="text-gray-600">输入关键词开始搜索</p>
        <p class="text-sm text-gray-500 mt-2">发现更多周同学相关内容</p>
      </div>
    </div>

    <!-- Detail Modal -->
    <div v-if="selectedItem" class="modal modal-open">
      <div class="modal-box max-w-2xl">
        <button
          @click="selectedItem = null"
          class="btn btn-sm btn-circle absolute right-2 top-2"
        >
          ✕
        </button>
        
        <h3 class="font-bold text-lg mb-4">{{ selectedItem.title }}</h3>
        
        <div class="space-y-4">
          <div class="flex items-center space-x-3">
            <div class="avatar placeholder">
              <div class="bg-neutral text-neutral-content rounded-full w-10 h-10">
                <span class="text-xs">{{ selectedItem.author.charAt(0) }}</span>
              </div>
            </div>
            <div>
              <div class="font-medium">{{ selectedItem.author }}</div>
              <div class="text-sm text-gray-500">{{ selectedItem.time }}</div>
            </div>
          </div>
          
          <div class="bg-gray-50 p-4 rounded-lg">
            <p class="text-gray-700">{{ selectedItem.description }}</p>
          </div>
          
          <div class="flex flex-wrap gap-2">
            <span
              v-for="tag in selectedItem.tags"
              :key="tag"
              class="badge badge-primary badge-outline"
            >
              {{ tag }}
            </span>
          </div>
          
          <div class="flex justify-between items-center pt-4 border-t">
            <div class="flex items-center space-x-6">
              <span class="flex items-center text-red-500">
                <span class="mr-1">❤️</span>
                {{ selectedItem.likes }}
              </span>
              <span class="flex items-center text-blue-500">
                <span class="mr-1">💬</span>
                {{ selectedItem.comments }}
              </span>
              <span class="flex items-center text-green-500">
                <span class="mr-1">🔄</span>
                {{ selectedItem.shares }}
              </span>
            </div>
            <a
              :href="selectedItem.link"
              target="_blank"
              class="btn btn-primary btn-sm"
            >
              查看原文
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { searchXiaoHongShuNotes, searchXiaoHongShuUsers, getHotTopics } from '../services/mcpSearchService.js'

const searchQuery = ref('')
const searchType = ref('notes')
const isSearching = ref(false)
const searchResults = ref([])
const selectedItem = ref(null)
const hasSearched = ref(false)
const sortType = ref('hot_desc')
const currentPage = ref(1)
const pageSize = 6

const quickTags = ref(getHotTopics().slice(0, 9))

const paginatedResults = computed(() => {
  const start = (currentPage.value - 1) * pageSize
  const end = start + pageSize
  return searchResults.value.slice(start, end)
})

const totalPages = computed(() => {
  return Math.ceil(searchResults.value.length / pageSize)
})

const performSearch = async () => {
  if (!searchQuery.value.trim()) return
  
  isSearching.value = true
  hasSearched.value = true
  
  try {
    // 调用我们的小红书搜索MCP工具
    const response = await callMCPSearch(searchQuery.value, searchType.value)
    searchResults.value = response
    currentPage.value = 1
  } catch (error) {
    console.error('搜索失败:', error)
    // 使用模拟数据作为fallback
    searchResults.value = generateMockResults(searchQuery.value)
  } finally {
    isSearching.value = false
  }
}

const callMCPSearch = async (keyword, type) => {
  try {
    if (type === 'notes') {
      return await searchXiaoHongShuNotes(keyword, {
        page: 1,
        sort_type: sortType.value
      })
    } else {
      return await searchXiaoHongShuUsers(keyword, {
        page: 1,
        sort_type: sortType.value
      })
    }
  } catch (error) {
    console.error('MCP搜索失败:', error)
    // 降级到模拟数据
    return generateMockResults(keyword)
  }
}

const generateMockResults = (keyword) => {
  const mockResults = []
  for (let i = 1; i <= 15; i++) {
    mockResults.push({
      title: `关于${keyword}的精彩分享 #${i}`,
      author: `博主${i}`,
      description: `这是一篇关于${keyword}的详细分享，包含实用技巧和心得体验。涵盖了最新的活动信息、打卡攻略、以及个人的真实感受和建议。`,
      likes: Math.floor(Math.random() * 500) + 50,
      comments: Math.floor(Math.random() * 100) + 10,
      shares: Math.floor(Math.random() * 50) + 5,
      tags: [keyword, '分享', '推荐', '攻略', '体验'].slice(0, Math.floor(Math.random() * 3) + 2),
      time: `2024-12-${String(Math.floor(Math.random() * 30) + 1).padStart(2, '0')}`,
      link: `https://www.xiaohongshu.com/explore/note_${i}`
    })
  }
  return mockResults
}

const quickSearch = (tag) => {
  searchQuery.value = tag
  performSearch()
}

const sortResults = () => {
  switch (sortType.value) {
    case 'hot_desc':
      searchResults.value.sort((a, b) => b.likes - a.likes)
      break
    case 'time_desc':
      searchResults.value.sort((a, b) => new Date(b.time) - new Date(a.time))
      break
    case 'like_desc':
      searchResults.value.sort((a, b) => b.likes - a.likes)
      break
  }
}

const openDetail = (item) => {
  selectedItem.value = item
}

onMounted(() => {
  // 组件挂载时可以执行一些初始化操作
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.xiaohongshu-search {
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}
</style> 