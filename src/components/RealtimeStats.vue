<template>
  <div class="bg-gradient-to-br from-purple-600 to-blue-600 text-white p-6 rounded-2xl shadow-xl">
    <div class="flex items-center justify-between mb-6">
      <h3 class="text-2xl font-bold flex items-center">
        <span class="animate-pulse text-green-400 mr-2">●</span>
        实时数据监控
      </h3>
      <div class="text-sm opacity-80">
        更新时间: {{ updateTime }}
      </div>
    </div>

    <!-- 核心数据 -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
      <div class="bg-white/10 backdrop-blur-sm rounded-lg p-4 text-center">
        <div class="text-2xl font-bold text-yellow-400">{{ formatNumber(realtimeData.totalCheckins) }}</div>
        <div class="text-sm opacity-80">总打卡数</div>
        <div class="text-xs text-green-300 mt-1">+{{ todayIncrease }} 今日</div>
      </div>
      
      <div class="bg-white/10 backdrop-blur-sm rounded-lg p-4 text-center">
        <div class="text-2xl font-bold text-green-400">{{ formatNumber(realtimeData.onlineUsers) }}</div>
        <div class="text-sm opacity-80">在线用户</div>
        <div class="text-xs text-blue-300 mt-1">{{ onlineGrowth }}% 增长</div>
      </div>
      
      <div class="bg-white/10 backdrop-blur-sm rounded-lg p-4 text-center">
        <div class="text-2xl font-bold text-pink-400">{{ formatNumber(realtimeData.todayPosts) }}</div>
        <div class="text-sm opacity-80">今日发布</div>
        <div class="text-xs text-purple-300 mt-1">{{ postGrowth }}% vs昨日</div>
      </div>
      
      <div class="bg-white/10 backdrop-blur-sm rounded-lg p-4 text-center">
        <div class="text-2xl font-bold text-cyan-400">{{ remainingDays }}</div>
        <div class="text-sm opacity-80">剩余天数</div>
        <div class="text-xs text-orange-300 mt-1">距离活动结束</div>
      </div>
    </div>

    <!-- 实时动态 -->
    <div class="bg-white/5 rounded-lg p-4 mb-4">
      <h4 class="text-lg font-semibold mb-3 flex items-center">
        <span class="w-2 h-2 bg-red-400 rounded-full animate-ping mr-2"></span>
        实时动态
      </h4>
      <div class="space-y-2 max-h-32 overflow-y-auto">
        <div v-for="(activity, index) in recentActivities" :key="index" 
             class="flex justify-between items-center text-sm p-2 bg-white/5 rounded">
          <span>{{ activity.text }}</span>
          <span class="text-xs opacity-60">{{ activity.time }}</span>
        </div>
      </div>
    </div>

    <!-- 热门话题 -->
    <div class="bg-white/5 rounded-lg p-4">
      <h4 class="text-lg font-semibold mb-3">🔥 实时热门</h4>
      <div class="space-y-2">
        <div v-for="(topic, index) in trendingTopics" :key="index" 
             class="flex justify-between items-center text-sm">
          <span class="flex items-center">
            <span class="w-4 h-4 bg-gradient-to-r from-red-400 to-pink-400 rounded text-xs text-white flex items-center justify-center mr-2">
              {{ index + 1 }}
            </span>
            #{{ topic.topic }}#
          </span>
          <span class="text-xs opacity-60">{{ formatNumber(topic.heat) }} 热度</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import realDataService from '../services/realDataService.js'

// 响应式数据
const realtimeData = ref({
  totalCheckins: 18500,
  onlineUsers: 2800,
  todayPosts: 450,
  trendingNow: []
})

const recentActivities = ref([])
const trendingTopics = ref([])
const updateTime = ref('')
const todayIncrease = ref(0)
const onlineGrowth = ref(0)
const postGrowth = ref(0)
const remainingDays = ref(calculateRemainingDays())

let updateInterval = null

// 方法
function calculateRemainingDays() {
  const endDate = new Date('2025-07-20')
  const today = new Date()
  const diffTime = endDate - today
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
}

function formatNumber(num) {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  }
  return num.toLocaleString()
}

async function updateRealtimeData() {
  try {
    const data = await realDataService.getRealtimeData()
    
    // 更新核心数据
    realtimeData.value = data
    
    // 更新实时动态
    recentActivities.value = data.recentActivity || []
    
    // 更新热门话题
    if (data.trendingNow && data.trendingNow.length > 0) {
      trendingTopics.value = data.trendingNow.slice(0, 5)
    }
    
    // 模拟增长数据
    todayIncrease.value = Math.floor(Math.random() * 500) + 200
    onlineGrowth.value = (Math.random() * 20 + 5).toFixed(1)
    postGrowth.value = (Math.random() * 15 + 3).toFixed(1)
    
    updateTime.value = new Date().toLocaleTimeString()
    
  } catch (error) {
    console.error('更新实时数据失败:', error)
  }
}

// 模拟新活动
function simulateNewActivity() {
  const activities = [
    '用户***刚刚在十里长堤打卡成功',
    '用户***兑换了限量版周杰伦篮球',
    '用户***发布了专列体验笔记',
    '用户***完成了六大打卡点挑战',
    '用户***参与了AR音乐互动',
    '用户***获得了地铁豆奖励',
    '用户***分享了厦门美食探店',
    '用户***在海上世界体验音乐台阶'
  ]
  
  const newActivity = {
    text: activities[Math.floor(Math.random() * activities.length)],
    time: new Date().toLocaleTimeString()
  }
  
  recentActivities.value.unshift(newActivity)
  if (recentActivities.value.length > 6) {
    recentActivities.value.pop()
  }
  
  // 更新计数器
  realtimeData.value.totalCheckins += Math.floor(Math.random() * 3) + 1
  realtimeData.value.onlineUsers += Math.floor(Math.random() * 20) - 10
  
  if (realtimeData.value.onlineUsers < 2000) {
    realtimeData.value.onlineUsers = 2000 + Math.floor(Math.random() * 1000)
  }
}

// 生命周期
onMounted(async () => {
  await updateRealtimeData()
  
  // 每30秒更新一次完整数据
  updateInterval = setInterval(updateRealtimeData, 30000)
  
  // 每5秒模拟新活动
  setInterval(simulateNewActivity, 5000)
})

onUnmounted(() => {
  if (updateInterval) {
    clearInterval(updateInterval)
  }
})
</script>

<style scoped>
/* 自定义滚动条 */
.overflow-y-auto::-webkit-scrollbar {
  width: 4px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
</style> 