/**
 * 小红书搜索MCP工具服务
 */

// MCP工具调用函数
const callMCPTool = async (toolName, params) => {
  try {
    const response = await fetch('/api/mcp-search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        tool: toolName,
        params: params
      })
    })
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    return data
  } catch (error) {
    console.error('MCP工具调用失败:', error)
    throw error
  }
}

// 搜索小红书笔记
export const searchXiaoHongShuNotes = async (keyword, options = {}) => {
  const defaultOptions = {
    page: 1,
    sort_type: 'hot_desc'
  }
  
  const params = {
    keyword,
    ...defaultOptions,
    ...options
  }
  
  try {
    const result = await callMCPTool('search_xiaohongshu_notes', params)
    return parseSearchResults(result)
  } catch (error) {
    console.warn('使用MCP工具失败，降级到模拟数据:', error)
    return generateMockResults(keyword, 'notes')
  }
}

// 搜索小红书用户
export const searchXiaoHongShuUsers = async (keyword, options = {}) => {
  const defaultOptions = {
    page: 1,
    sort_type: 'hot_desc'
  }
  
  const params = {
    keyword,
    ...defaultOptions,
    ...options
  }
  
  try {
    const result = await callMCPTool('search_xiaohongshu_users', params)
    return parseUserResults(result)
  } catch (error) {
    console.warn('使用MCP工具失败，降级到模拟数据:', error)
    return generateMockResults(keyword, 'users')
  }
}

// 解析搜索结果
const parseSearchResults = (mcpResult) => {
  // 这里需要根据MCP工具的实际返回格式来解析
  // 目前我们的MCP工具返回的是文本格式，需要解析
  if (mcpResult && mcpResult.content && mcpResult.content.length > 0) {
    const textContent = mcpResult.content[0].text
    return parseMCPTextResult(textContent)
  }
  return []
}

// 解析用户结果
const parseUserResults = (mcpResult) => {
  if (mcpResult && mcpResult.content && mcpResult.content.length > 0) {
    const textContent = mcpResult.content[0].text
    return parseMCPUserResult(textContent)
  }
  return []
}

// 解析MCP文本结果为结构化数据
const parseMCPTextResult = (textContent) => {
  const results = []
  
  // 通过正则表达式解析文本内容
  const notePattern = /📝 笔记 (\d+)\n\s*标题:\s*(.*?)\n\s*作者:\s*(.*?)\n\s*描述:\s*(.*?)\n\s*互动:\s*❤️\s*(\d+)\s*💬\s*(\d+)\s*🔄\s*(\d+)\n\s*标签:\s*(.*?)\n\s*时间:\s*(.*?)\n\s*链接:\s*(.*?)\n/g
  
  let match
  while ((match = notePattern.exec(textContent)) !== null) {
    const [, id, title, author, description, likes, comments, shares, tags, time, link] = match
    
    results.push({
      id,
      title: title.trim(),
      author: author.trim(),
      description: description.trim(),
      likes: parseInt(likes),
      comments: parseInt(comments),
      shares: parseInt(shares),
      tags: tags.split(',').map(tag => tag.trim()),
      time: time.trim(),
      link: link.trim()
    })
  }
  
  return results
}

// 解析MCP用户结果
const parseMCPUserResult = (textContent) => {
  const results = []
  
  // 类似的解析逻辑，但针对用户搜索结果
  const userPattern = /👤 用户 (\d+)\n\s*昵称:\s*(.*?)\n\s*简介:\s*(.*?)\n\s*粉丝:\s*(\d+)\n\s*获赞:\s*(\d+)\n\s*笔记:\s*(\d+)\n\s*链接:\s*(.*?)\n/g
  
  let match
  while ((match = userPattern.exec(textContent)) !== null) {
    const [, id, nickname, bio, followers, likes, notes, link] = match
    
    results.push({
      id,
      nickname: nickname.trim(),
      bio: bio.trim(),
      followers: parseInt(followers),
      likes: parseInt(likes),
      notes: parseInt(notes),
      link: link.trim()
    })
  }
  
  return results
}

// 生成模拟数据作为降级方案
const generateMockResults = (keyword, type = 'notes') => {
  const mockResults = []
  
  if (type === 'notes') {
    for (let i = 1; i <= 15; i++) {
      mockResults.push({
        id: i,
        title: `关于${keyword}的精彩分享 #${i}`,
        author: `博主${i}`,
        description: `这是一篇关于${keyword}的详细分享，包含实用技巧和心得体验。涵盖了最新的活动信息、打卡攻略、以及个人的真实感受和建议。`,
        likes: Math.floor(Math.random() * 500) + 50,
        comments: Math.floor(Math.random() * 100) + 10,
        shares: Math.floor(Math.random() * 50) + 5,
        tags: [keyword, '分享', '推荐', '攻略', '体验'].slice(0, Math.floor(Math.random() * 3) + 2),
        time: `2024-12-${String(Math.floor(Math.random() * 30) + 1).padStart(2, '0')}`,
        link: `https://www.xiaohongshu.com/explore/note_${i}`
      })
    }
  } else if (type === 'users') {
    for (let i = 1; i <= 10; i++) {
      mockResults.push({
        id: i,
        nickname: `${keyword}达人${i}`,
        bio: `专注${keyword}相关内容创作，分享生活美好瞬间`,
        followers: Math.floor(Math.random() * 10000) + 1000,
        likes: Math.floor(Math.random() * 50000) + 5000,
        notes: Math.floor(Math.random() * 200) + 50,
        link: `https://www.xiaohongshu.com/user/profile/${i}`
      })
    }
  }
  
  return mockResults
}

// 直接调用Python MCP工具的函数（开发时使用）
export const directMCPCall = async (toolName, params) => {
  try {
    const request = {
      jsonrpc: '2.0',
      id: Date.now(),
      method: 'tools/call',
      params: {
        name: toolName,
        arguments: params
      }
    }
    
    // 这里可以直接调用Python脚本
    const response = await fetch('/api/direct-mcp', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request)
    })
    
    const data = await response.json()
    return data.result
  } catch (error) {
    console.error('直接MCP调用失败:', error)
    throw error
  }
}

// 获取热门话题
export const getHotTopics = () => {
  return [
    '周同学CHOUCHOU厦门站',
    '厦门地铁',
    '周杰伦',
    '打卡攻略',
    '快闪店',
    '地铁豆',
    '十里长堤',
    '海上世界',
    '鼓浪屿',
    '中山路',
    'SM城市广场',
    '荻花洲',
    '星际光轮篮球',
    '潮汐之眼冰箱贴',
    '音乐台阶',
    'AR互动',
    '草坪音乐会',
    '骑楼街区',
    '钢琴码头',
    '艺术装置'
  ]
}

// 获取搜索建议
export const getSearchSuggestions = (keyword) => {
  const suggestions = [
    `${keyword} 攻略`,
    `${keyword} 打卡`,
    `${keyword} 体验`,
    `${keyword} 推荐`,
    `${keyword} 分享`,
    `${keyword} 评测`,
    `${keyword} 测评`,
    `${keyword} 心得`
  ]
  
  return suggestions.filter(s => s.length > 0)
}

export default {
  searchXiaoHongShuNotes,
  searchXiaoHongShuUsers,
  directMCPCall,
  getHotTopics,
  getSearchSuggestions
} 