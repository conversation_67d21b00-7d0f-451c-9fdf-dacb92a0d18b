{"version": "2.0", "envId": "huiyi-7gudxwsnf4ca4b67", "$schema": "https://framework-1258016615.tcloudbaseapp.com/schema/latest.json", "framework": {"name": "cloudbase-vue-template", "plugins": {"client": {"use": "@cloudbase/framework-plugin-website", "inputs": {"outputPath": "dist", "buildCommand": "npm run build", "cloudPath": "/zhou-activities"}}, "server": {"use": "@cloudbase/framework-plugin-function", "inputs": {"functionRootPath": "cloudfunctions", "functions": [{"name": "xiaohongshu-search", "timeout": 30, "envVariables": {"NODE_ENV": "production"}, "runtime": "Nodejs18.15", "memorySize": 256, "installDependency": true}]}}}}, "functions": [{"name": "xiaohongshu-search", "timeout": 30, "envVariables": {"NODE_ENV": "production"}, "runtime": "Nodejs18.15", "memorySize": 256, "installDependency": true}]}